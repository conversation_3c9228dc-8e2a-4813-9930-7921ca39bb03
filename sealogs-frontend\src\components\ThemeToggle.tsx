'use client'

import React from 'react'
import { <PERSON>, Sun, Monitor } from 'lucide-react'
import {
    DropdownMenuItem,
    DropdownMenuPortal,
    DropdownMenuSub,
    DropdownMenuSubContent,
    DropdownMenuSubTrigger,
} from '@/components/ui/dropdown-menu'
import { useTheme } from 'next-themes'

const SidebarTheme = () => {
    const { theme, setTheme } = useTheme()

    const getThemeIcon = () => {
        switch (theme) {
            case 'dark':
                return <Moon className="h-4 w-4" />
            case 'light':
                return <Sun className="h-4 w-4" />
            default:
                return <Monitor className="h-4 w-4" />
        }
    }

    return (
        <DropdownMenuSub>
            <DropdownMenuSubTrigger>
                {getThemeIcon()}
                Theme
            </DropdownMenuSubTrigger>
            <DropdownMenuPortal>
                <DropdownMenuSubContent>
                    <DropdownMenuItem onClick={() => setTheme('light')}>
                        <Sun className="h-4 w-4" />
                        Light
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setTheme('dark')}>
                        <Moon className="h-4 w-4" />
                        Dark
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setTheme('system')}>
                        <Monitor className="h-4 w-4" />
                        System
                    </DropdownMenuItem>
                </DropdownMenuSubContent>
            </DropdownMenuPortal>
        </DropdownMenuSub>
    )
}

export default SidebarTheme
