'use client'

import { useEffect, useRef, useState } from 'react'
import { useToast } from '@/hooks/use-toast'
import FileUpload from '../../../components/file-upload'
import { useLazyQuery, useMutation } from '@apollo/client'
import GET_OTHER_COMPANIES from '@/app/lib/graphQL/query/GET_OTHER_COMPANIES'
import { InputSkeleton } from '../../../components/skeletons'
import DialogCreateCompany from './dialog-create-company'
import { GET_KEY_CONTACT_BY_ID } from '@/app/lib/graphQL/query'
import FileItem from '@/components/file-item'
import { Check, Users, XCircle as XCircleIcon } from 'lucide-react'
import {
    CREATE_KEY_CONTACT,
    UPDATE_KEY_CONTACT,
} from '@/app/lib/graphQL/mutation'
import { useRouter } from 'next/navigation'
import DeleteKeyContact from './delete-key-contact'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { IKeyContactPermission } from './key-contact-list'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Combobox } from '@/components/ui/comboBox'
import { Button } from '@/components/ui/button'
import { ListHeader } from '@/components/ui'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { H4, P } from '@/components/ui/typography'
import { FooterWrapper } from '@/components/footer-wrapper'

interface IProps {
    id?: number
}

interface ISelectItem {
    value: string
    label: string
}

interface IFormValue {
    firstName: string
    surname: string
    phone?: string
    cellPhone?: string
    email?: string
    vhfChannel?: number | string
    address?: string
}

export default function KeyContactForm({ id }: IProps) {
    const [permission, setPermission] = useState<IKeyContactPermission>({
        EDIT_KEY_CONTACT: undefined,
        DELETE_KEY_CONTACT: undefined,
    })
    const formRef = useRef<HTMLFormElement>(null)
    const router = useRouter()
    const { toast } = useToast()

    //form section
    const [formValue, setFormValue] = useState<IFormValue>({
        firstName: '',
        surname: '',
        phone: '',
        cellPhone: '',
        email: '',
        vhfChannel: '',
        address: '',
    })
    const [attachments, setAttachments] = useState<Array<Record<string, any>>>(
        [],
    )

    const deleteFile = async (id: number) => {
        setAttachments((prev) => prev.filter((doc) => doc.id !== id))
    }

    const handleInputChange = <K extends keyof IFormValue>(
        name: K,
        value: string,
    ) => {
        setFormValue((prev) => ({ ...prev, [name]: value }))
    }
    //end form section

    //start handle select company
    const [openDialogNewCompany, setOpenDialogNewCompany] = useState(false)
    const [selectedCompany, setSelectedCompany] = useState<ISelectItem | null>(
        null,
    )
    const [companies, setCompanies] = useState<ISelectItem[]>([])
    const [
        queryGetCompanies,
        { called: calledCompanies, loading: loadingCompanies },
    ] = useLazyQuery(GET_OTHER_COMPANIES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOtherCompanies.nodes
            const companiesSelect: ISelectItem[] = [
                {
                    value: 'newOtherCompany',
                    label: ' ---- Create Company ---- ',
                },
                ...data.map((item: any) => ({
                    value: item.id,
                    label: item.title,
                })),
            ]
            setCompanies(companiesSelect)
        },
        onError: (error: any) => {
            console.error('queryGetCompanies error', error)
        },
    })

    const onCompanySelectChange = (selected: ISelectItem) => {
        if (selected.value === 'newOtherCompany') {
            setOpenDialogNewCompany(true)
            return
        }

        setSelectedCompany(selected)
    }

    const onCreateNewCompanySuccess = (newCompany: any) => {
        const selectItem = {
            value: newCompany.id,
            label: newCompany.title,
        }

        setCompanies((prev) => [...prev, selectItem])

        setSelectedCompany(selectItem)
    }

    //end handle select company

    // edit key contact section
    const loadKeyContact = async (id: number) => {
        const { data } = await queryGetKeyContactByID({
            variables: { filter: { id: { eq: id } } },
        })

        const existingKeyContact = data.readOneKeyContact

        setFormValue({
            firstName: existingKeyContact?.firstName || '',
            surname: existingKeyContact?.surname || '',
            phone: existingKeyContact?.phone || '',
            cellPhone: existingKeyContact?.cellPhone || '',
            email: existingKeyContact?.email || '',
            vhfChannel: existingKeyContact?.vhfChannel || '',
            address: existingKeyContact?.address || '',
        })

        const contactCompany = existingKeyContact?.company
        if (contactCompany) {
            setSelectedCompany({
                value: contactCompany.id,
                label: contactCompany.title,
            })
        }

        if (existingKeyContact?.attachments.nodes.length > 0) {
            setAttachments(existingKeyContact?.attachments.nodes)
        }
    }

    const [queryGetKeyContactByID] = useLazyQuery(GET_KEY_CONTACT_BY_ID, {
        fetchPolicy: 'cache-and-network',
        onError: (error: any) => {
            console.error('getKeyContactByID error', error)
        },
    })
    //edit key contact section

    //submit handler
    const [
        saveMutationKeyContact,
        { called: saveMutationCalled, loading: saveMutationLoading },
    ] = useMutation(id ? UPDATE_KEY_CONTACT : CREATE_KEY_CONTACT, {
        onCompleted: () => {
            router.push('/key-contacts')
        },
        onError: (error: any) => {
            console.error('mutationcreateSupplier error', error)
        },
    })

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()

        if (permission.EDIT_KEY_CONTACT === false) {
            toast({
                variant: 'destructive',
                title: 'Permission Error',
                description:
                    "You don't have permission to create or update key contact!",
            })
            return
        }

        const input = {
            id: id || '',
            ...formValue,
            vhfChannel: formValue.vhfChannel
                ? parseInt(`${formValue.vhfChannel ?? '0'}`)
                : null,
            companyID: selectedCompany?.value ?? '',
            attachments: attachments.map((item: any) => item.id).join(','),
        }

        saveMutationKeyContact({ variables: { input } })
    }

    //end submit handler

    const initPermission = () => {
        const permissions = getPermissions('EDIT_KEY_CONTACT')

        Object.keys(permission).forEach((value) => {
            const hasThisPermission = hasPermission(value, permissions)

            setPermission((prev) => ({ ...prev, [value]: hasThisPermission }))
        })
    }

    //fetch key contact detail if id is > 0
    useEffect(() => {
        if (id) {
            loadKeyContact(id)
        }
    }, [id])

    useEffect(() => {
        queryGetCompanies()
        initPermission()
    }, [])

    return (
        <div className="space-y-6">
            <ListHeader icon={<Users className="h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]" />} title={id ? 'Edit Key Contact' : 'Add Key Contact'} />

            <Card className="mx-2.5">
                <CardHeader>
                    <H4>Key Contact Details</H4>
                    <P>
                        Record contact information for key personnel. Make sure
                        all details are accurate and up-to-date for effective
                        communication.
                    </P>
                </CardHeader>
                <CardContent className="space-y-8">
                    <form onSubmit={handleSubmit} ref={formRef}>
                        <div className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                                <Label label="First name" htmlFor="firstName">
                                    <Input
                                        id="firstName"
                                        type="text"
                                        placeholder="First name"
                                        name="firstName"
                                        value={formValue.firstName}
                                        required
                                        onChange={(e) =>
                                            handleInputChange(
                                                e.target.name as any,
                                                e.target.value,
                                            )
                                        }
                                    />
                                </Label>
                                <Label label="Surname" htmlFor="surname">
                                    <Input
                                        id="surname"
                                        type="text"
                                        placeholder="Surname"
                                        name="surname"
                                        value={formValue.surname}
                                        onChange={(e) =>
                                            handleInputChange(
                                                e.target.name as any,
                                                e.target.value,
                                            )
                                        }
                                    />
                                </Label>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                                <Label label="Phone" htmlFor="phone">
                                    <Input
                                        id="phone"
                                        type="text"
                                        placeholder="Phone"
                                        name="phone"
                                        value={formValue.phone}
                                        onChange={(e) =>
                                            handleInputChange(
                                                e.target.name as any,
                                                e.target.value,
                                            )
                                        }
                                    />
                                </Label>
                                <Label label="Cell Phone" htmlFor="cellPhone">
                                    <Input
                                        id="cellPhone"
                                        type="text"
                                        placeholder="Cell Phone"
                                        name="cellPhone"
                                        value={formValue.cellPhone}
                                        onChange={(e) =>
                                            handleInputChange(
                                                e.target.name as any,
                                                e.target.value,
                                            )
                                        }
                                    />
                                </Label>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                                <Label label="Email" htmlFor="email">
                                    <Input
                                        id="email"
                                        type="email"
                                        placeholder="Email"
                                        name="email"
                                        value={formValue.email}
                                        onChange={(e) =>
                                            handleInputChange(
                                                e.target.name as any,
                                                e.target.value,
                                            )
                                        }
                                    />
                                </Label>
                                <Label label="VHF Channel" htmlFor="vhfChannel">
                                    <Input
                                        id="vhfChannel"
                                        type="number"
                                        placeholder="VHF Channel"
                                        name="vhfChannel"
                                        value={formValue.vhfChannel}
                                        onChange={(e) =>
                                            handleInputChange(
                                                e.target.name as any,
                                                e.target.value,
                                            )
                                        }
                                    />
                                </Label>
                            </div>
                            <Label label="Address" htmlFor="address">
                                <Textarea
                                    id="address"
                                    name="address"
                                    value={formValue.address}
                                    onChange={(e) =>
                                        handleInputChange(
                                            e.target.name as any,
                                            e.target.value,
                                        )
                                    }
                                    placeholder="Address"
                                    rows={3}
                                />
                            </Label>
                            <Label label="Company" htmlFor="company">
                                {calledCompanies && loadingCompanies ? (
                                    <InputSkeleton />
                                ) : (
                                    <Combobox
                                        id="company"
                                        options={companies.map((company) => ({
                                            label: company.label,
                                            value: company.value,
                                        }))}
                                        value={
                                            selectedCompany
                                                ? {
                                                      label: selectedCompany.label,
                                                      value: selectedCompany.value,
                                                  }
                                                : undefined
                                        }
                                        onChange={(selected: any) => {
                                            if (selected) {
                                                onCompanySelectChange({
                                                    value: selected.value,
                                                    label: selected.label,
                                                })
                                            }
                                        }}
                                        placeholder="Select Company"
                                    />
                                )}
                            </Label>
                        </div>
                    </form>
                </CardContent>
            </Card>
            <Card className="mx-2.5">
                <CardHeader>
                    <H4>Attachments</H4>
                    <P>
                        Upload relevant documents and files for this key
                        contact.
                    </P>
                </CardHeader>
                <CardContent className="space-y-4">
                    <FileUpload
                        setDocuments={setAttachments}
                        text=""
                        subText="Drag files here or upload"
                        bgClass="bg-slblue-50"
                        documents={attachments}
                        // multipleUpload
                    />
                    {attachments.length > 0 && (
                        <div className="space-y-3">
                            {attachments.map((document: any) => (
                                <div
                                    key={document.id}
                                    className="flex items-center justify-between bg-accent rounded-md border border-border border-dashed">
                                    <FileItem document={document} />
                                    <Button
                                        variant='destructive'
                                        size='icon'
                                        onClick={() => deleteFile(document.id)}
                                        className="text-destructive mx-2.5 hover:text-destructive/80"
                                        iconLeft={XCircleIcon}
                                        iconOnly
                                    />
                                </div>
                            ))}
                        </div>
                    )}
                </CardContent>
            </Card>
            <FooterWrapper>
                <Button
                    variant="back"
                    onClick={() => router.push('/key-contacts')}>
                    Cancel
                </Button>
                {id && permission.DELETE_KEY_CONTACT === true && (
                    <DeleteKeyContact
                        id={id}
                        fullName={`${formValue.firstName} ${formValue.surname}`}
                    />
                )}
                <Button
                    disabled={
                        permission.EDIT_KEY_CONTACT === false ||
                        (saveMutationCalled && saveMutationLoading)
                    }
                    onClick={() => formRef.current?.requestSubmit()}
                    iconLeft={Check}>
                    Save
                </Button>
            </FooterWrapper>

            <DialogCreateCompany
                isOpen={openDialogNewCompany}
                setIsOpen={setOpenDialogNewCompany}
                onCreateSuccess={onCreateNewCompanySuccess}
            />
        </div>
    )
}
