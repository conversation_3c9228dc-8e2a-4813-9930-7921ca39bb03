"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/suppliers/new/page",{

/***/ "(app-pages-browser)/./src/app/ui/inventory/supplier-new.tsx":
/*!***********************************************!*\
  !*** ./src/app/ui/inventory/supplier-new.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewSupplier; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation_CREATE_SUPPLIER_CONTACT__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/lib/graphQL/mutation/CREATE_SUPPLIER_CONTACT */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/CREATE_SUPPLIER_CONTACT.ts\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _supplier_contacts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./supplier-contacts */ \"(app-pages-browser)/./src/app/ui/inventory/supplier-contacts.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NewSupplier(param) {\n    let { supplierId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [fileLinks, setFileLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [linkSelectedOption, setLinkSelectedOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [contactFields, setContactFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            name: \"\",\n            email: \"\",\n            phone: \"\"\n        }\n    ]);\n    const handleCreate = async ()=>{\n        var _response_data;\n        const name = document.getElementById(\"supplier-name\").value;\n        const website = document.getElementById(\"supplier-website\").value;\n        const phone = document.getElementById(\"supplier-phone\").value;\n        const email = document.getElementById(\"supplier-email\").value;\n        const address = document.getElementById(\"supplier-address\").value;\n        const notes = document.getElementById(\"supplier-notes\").value;\n        const variables = {\n            input: {\n                name,\n                address,\n                website,\n                email,\n                phone,\n                notes\n            }\n        };\n        if (name === \"\") {\n            return toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Please fill supplier's name!\"\n            });\n        }\n        // if (contactFields.length > 0) {\n        //     const anyContactFieldEmpty = contactFields.filter(\n        //         (contactField) =>\n        //             contactField.name == '' ||\n        //             (contactField.phone == '' && contactField.email == ''),\n        //     ).length\n        //     if (anyContactFieldEmpty > 0) {\n        //         return toast({\n        //             variant: 'destructive',\n        //             title: 'Error',\n        //             description: 'Please complete contact data!',\n        //         })\n        //     }\n        // }\n        const response = await mutationCreateSupplier({\n            variables\n        });\n        var _response_data_createSupplier_id;\n        const supplierID = (_response_data_createSupplier_id = (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.createSupplier.id) !== null && _response_data_createSupplier_id !== void 0 ? _response_data_createSupplier_id : 0;\n        if (supplierID == 0) {\n            return toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Error creating new supplier\"\n            });\n        }\n        contactFields.forEach(async (element)=>{\n            const variableContact = {\n                ...element,\n                supplierID\n            };\n            delete variableContact.id;\n            await mutationCreateSupplierContact({\n                variables: {\n                    input: variableContact\n                }\n            });\n        });\n        router.back();\n    };\n    const [mutationCreateSupplier, { loading: mutationcreateSupplierLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_SUPPLIER, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"mutationcreateSupplier error\", error);\n        }\n    });\n    const [mutationCreateSupplierContact, { loading: mutationcreateSupplierContactLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useMutation)(_app_lib_graphQL_mutation_CREATE_SUPPLIER_CONTACT__WEBPACK_IMPORTED_MODULE_11__.CREATE_SUPPLIER_CONTACT, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"mutationcreateSupplierContact error\", error);\n        }\n    });\n    const [createSeaLogsFileLinks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_SEALOGS_FILE_LINKS, {\n        onCompleted: (response)=>{\n            const data = response.createSeaLogsFileLinks;\n            if (data.id > 0) {\n                const newLinks = [\n                    ...fileLinks,\n                    data\n                ];\n                setFileLinks(newLinks);\n                linkSelectedOption ? setLinkSelectedOption([\n                    ...linkSelectedOption,\n                    {\n                        label: data.link,\n                        value: data.id\n                    }\n                ]) : setLinkSelectedOption([\n                    {\n                        label: data.link,\n                        value: data.id\n                    }\n                ]);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createSeaLogsFileLinksEntry error\", error);\n        }\n    });\n    const handleDeleteLink = (link)=>{\n        setLinkSelectedOption(linkSelectedOption.filter((l)=>l !== link));\n    };\n    const linkItem = (link)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-between align-middle mr-2 w-fit\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    href: link.label,\n                    target: \"_blank\",\n                    className: \"ml-2 \",\n                    children: link.label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-2 \",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                        iconOnly: true,\n                        onClick: ()=>handleDeleteLink(link)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n            lineNumber: 172,\n            columnNumber: 13\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Card, {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.H4, {\n                                children: \"New Supplier\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium flex items-center gap-2 text-primary\",\n                                        children: \"Company Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground text-sm leading-relaxed\",\n                                        children: \"Enter the basic information about the supplier company including contact details and address.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2 space-y-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        label: \"Company Name\",\n                                        htmlFor: \"supplier-name\",\n                                        className: \"text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            id: \"supplier-name\",\n                                            type: \"text\",\n                                            placeholder: \"Supplier name\",\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        label: \"Website\",\n                                        htmlFor: \"supplier-website\",\n                                        className: \"text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            id: \"supplier-website\",\n                                            type: \"text\",\n                                            placeholder: \"Company website\",\n                                            className: \"w-full\",\n                                            onKeyDown: async (event)=>{\n                                                if (event.key === \"Enter\") {\n                                                    const inputValue = event.target.value;\n                                                    await createSeaLogsFileLinks({\n                                                        variables: {\n                                                            input: {\n                                                                link: inputValue\n                                                            }\n                                                        }\n                                                    });\n                                                    toast({\n                                                        title: \"Website added\",\n                                                        description: \"Added \".concat(inputValue, \" to supplier links\")\n                                                    });\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 25\n                                    }, this),\n                                    (linkSelectedOption || fileLinks).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            label: \"Linked Websites\",\n                                            className: \"text-sm font-medium\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: linkSelectedOption ? linkSelectedOption.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-block\",\n                                                        children: linkItem(link)\n                                                    }, link.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 55\n                                                    }, this)) : fileLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-block\",\n                                                        children: linkItem(link)\n                                                    }, link.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 55\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                label: \"Phone Number\",\n                                                htmlFor: \"supplier-phone\",\n                                                className: \"text-sm font-medium\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    id: \"supplier-phone\",\n                                                    type: \"text\",\n                                                    placeholder: \"Phone number\",\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 33\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                label: \"Email Address\",\n                                                htmlFor: \"supplier-email\",\n                                                className: \"text-sm font-medium\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    id: \"supplier-email\",\n                                                    type: \"email\",\n                                                    placeholder: \"Email address\",\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 33\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        label: \"Address\",\n                                        htmlFor: \"supplier-address\",\n                                        className: \"text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                            id: \"supplier-address\",\n                                            rows: 3,\n                                            placeholder: \"Supplier address\",\n                                            className: \"w-full resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium flex items-center gap-2 text-primary\",\n                                        children: \"Contact Persons\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground text-sm leading-relaxed\",\n                                        children: \"Enter the contact details (name, phone, and email) of the supplier's representative.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_supplier_contacts__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    data: contactFields,\n                                    setData: setContactFields\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium flex items-center gap-2 text-primary\",\n                                        children: \"Additional Notes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground text-sm leading-relaxed\",\n                                        children: \"Add any additional information about this supplier that might be useful.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    label: \"Notes\",\n                                    htmlFor: \"supplier-notes\",\n                                    className: \"text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        id: \"supplier-notes\",\n                                        rows: 5,\n                                        placeholder: \"Enter any additional notes about this supplier...\",\n                                        className: \"w-full resize-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                lineNumber: 191,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_12__.FooterWrapper, {\n                className: \"mt-5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                        onClick: ()=>router.back(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: handleCreate,\n                        children: \"Create supplier\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                lineNumber: 384,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(NewSupplier, \"q/JrGpMPggXsrHp+ThyS7zkm6Zc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useMutation\n    ];\n});\n_c = NewSupplier;\nvar _c;\n$RefreshReg$(_c, \"NewSupplier\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/inventory/supplier-new.tsx\n"));

/***/ })

});