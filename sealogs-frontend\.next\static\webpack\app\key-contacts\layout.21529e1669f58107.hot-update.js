"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/key-contacts/layout",{

/***/ "(app-pages-browser)/./src/components/app-sidebar.tsx":
/*!****************************************!*\
  !*** ./src/components/app-sidebar.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: function() { return /* binding */ AppSidebar; },\n/* harmony export */   NavData: function() { return /* binding */ NavData; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _components_team__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/team */ \"(app-pages-browser)/./src/components/team.tsx\");\n/* harmony import */ var _nav_main__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./nav-main */ \"(app-pages-browser)/./src/components/nav-main.tsx\");\n/* harmony import */ var _nav_user__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./nav-user */ \"(app-pages-browser)/./src/components/nav-user.tsx\");\n/* harmony import */ var _nav_single_links__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./nav-single-links */ \"(app-pages-browser)/./src/components/nav-single-links.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n//logo versions: logo-small-dark.png | logo-small.png\nconst NavData = {\n    user: {\n        name: \"SeaLogs\",\n        email: \"<EMAIL>\"\n    },\n    team: {\n        name: \"South Inc\",\n        logo: \"logo-small.png\",\n        plan: \"powered by SeaLogs\"\n    },\n    versions: [\n        \"3.4.0\",\n        \"1.1.0-alpha\",\n        \"2.0.0-beta1\"\n    ],\n    singleLinks: [\n        {\n            name: \"Home port\",\n            url: \"/dashboard\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_7__.SealogsDashboardIcon, {\n                className: \"h-9 w-9\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 42,\n                columnNumber: 19\n            }, undefined)\n        },\n        {\n            name: \"All vessels\",\n            url: \"/vessel\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_7__.SealogsVesselsIcon, {\n                className: \"h-9 w-9 bg-accent/0 ring-0 rounded-none p-0 group-data-[collapsible=icon]:-ml-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 48,\n                columnNumber: 17\n            }, undefined)\n        }\n    ],\n    navMain: [\n        {\n            title: \"Crew\",\n            url: \"#\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_7__.SealogsCrewIcon, {\n                className: \"h-9 w-9 p-0.5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 56,\n                columnNumber: 19\n            }, undefined),\n            items: [\n                {\n                    title: \"All crew\",\n                    url: \"/crew\"\n                },\n                {\n                    title: \"Training / Drills\",\n                    url: \"/crew-training\"\n                }\n            ]\n        },\n        {\n            title: \"Health & safety\",\n            url: \"#\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_7__.SealogsHealthSafetyIcon, {\n                className: \"h-9 w-9\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 71,\n                columnNumber: 19\n            }, undefined),\n            items: [\n                {\n                    title: \"Risk Evaluations\",\n                    url: \"/risk-evaluations\"\n                },\n                {\n                    title: \"Risk Strategies\",\n                    url: \"/risk-strategies\"\n                },\n                {\n                    title: \"Drills / training matrix\",\n                    url: \"/training-matrix\"\n                }\n            ]\n        }\n    ],\n    navMain2: [\n        {\n            title: \"Inventory\",\n            url: \"#\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_7__.SealogsInventoryIcon, {\n                className: \"h-9 w-9\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 92,\n                columnNumber: 19\n            }, undefined),\n            items: [\n                {\n                    title: \"All inventory\",\n                    url: \"/inventory\"\n                },\n                {\n                    title: \"Suppliers\",\n                    url: \"/inventory/suppliers\"\n                },\n                {\n                    title: \"Maintenance\",\n                    url: \"/maintenance\"\n                },\n                {\n                    title: \"Documents\",\n                    url: \"/document-locker\"\n                }\n            ]\n        }\n    ]\n};\nfunction AppSidebar(param) {\n    let { ...props } = param;\n    _s();\n    const [clientTitle, setClientTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loggedUserName, setLoggedUserName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loggedEmail, setLoggedEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            setClientTitle(localStorage.getItem(\"clientTitle\") || \"\");\n            // init()\n            setIsLoading(false);\n        }\n        if (true) {\n            var _localStorage_getItem, _localStorage_getItem1;\n            setLoggedUserName(\"\".concat((_localStorage_getItem = localStorage.getItem(\"firstName\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : \"\", \" \").concat((_localStorage_getItem1 = localStorage.getItem(\"surname\")) !== null && _localStorage_getItem1 !== void 0 ? _localStorage_getItem1 : \"\"));\n            const user = localStorage.getItem(\"user\");\n            if (user !== null) {\n                var _JSON_parse_email;\n                setLoggedEmail(\"\".concat((_JSON_parse_email = JSON.parse(user).email) !== null && _JSON_parse_email !== void 0 ? _JSON_parse_email : \"\"));\n            }\n        }\n    }, [\n        isLoading\n    ]);\n    // Memoize the team data to prevent unnecessary re-renders\n    const teamData = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(()=>({\n            ...NavData.team,\n            name: clientTitle || NavData.team.name\n        }), [\n        clientTitle\n    ]);\n    // Memoize the user data to prevent unnecessary re-renders\n    const userData = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(()=>({\n            ...NavData.user,\n            name: loggedUserName || NavData.user.name,\n            email: loggedEmail || NavData.user.email\n        }), [\n        loggedUserName,\n        loggedEmail\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {\n        collapsible: \"icon\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_team__WEBPACK_IMPORTED_MODULE_3__.Team, {\n                    team: teamData\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 157,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nav_single_links__WEBPACK_IMPORTED_MODULE_6__.NavSingle, {\n                        projects: NavData.singleLinks\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nav_main__WEBPACK_IMPORTED_MODULE_4__.NavMain, {\n                        items: NavData.navMain\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nav_main__WEBPACK_IMPORTED_MODULE_4__.NavMain, {\n                        items: NavData.navMain2\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 160,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarFooter, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nav_user__WEBPACK_IMPORTED_MODULE_5__.NavUser, {\n                    user: userData\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 165,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarRail, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 168,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n        lineNumber: 156,\n        columnNumber: 9\n    }, this);\n}\n_s(AppSidebar, \"v2W/Zy20IFCysfNUmYctQ7qMJfU=\");\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/app-sidebar.tsx\n"));

/***/ })

});