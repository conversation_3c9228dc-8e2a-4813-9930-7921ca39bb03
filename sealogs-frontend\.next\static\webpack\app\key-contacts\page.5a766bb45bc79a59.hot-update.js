"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/key-contacts/page",{

/***/ "(app-pages-browser)/./src/app/ui/key-contacts/key-contact-list.tsx":
/*!******************************************************!*\
  !*** ./src/app/ui/key-contacts/key-contact-list.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ KeyContactList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_filter_components_supplier_list_actions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/filter/components/supplier-list-actions */ \"(app-pages-browser)/./src/components/filter/components/supplier-list-actions.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction KeyContactList() {\n    _s();\n    const [keyContacts, setKeyContacts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [groupByCompany, setGroupByCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permission, setPermission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        EDIT_KEY_CONTACT: undefined,\n        DELETE_KEY_CONTACT: undefined\n    });\n    const [queryGetKeyContacts, { loading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_10__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.GET_KEY_CONTACTS, {\n        fetchPolicy: \"cache-and-network\",\n        onError: (error)=>{\n            console.error(\"queryGetKeyContacts error\", error);\n        }\n    });\n    const loadData = async ()=>{\n        const { data } = await queryGetKeyContacts();\n        var _data_readKeyContacts_nodes;\n        setKeyContacts((_data_readKeyContacts_nodes = data === null || data === void 0 ? void 0 : data.readKeyContacts.nodes) !== null && _data_readKeyContacts_nodes !== void 0 ? _data_readKeyContacts_nodes : []);\n    };\n    const initPermission = ()=>{\n        const permissions = (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_4__.getPermissions)(\"EDIT_KEY_CONTACT\");\n        Object.keys(permission).forEach((value)=>{\n            const hasThisPermission = (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_4__.hasPermission)(value, permissions);\n            setPermission((prev)=>({\n                    ...prev,\n                    [value]: hasThisPermission\n                }));\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadData();\n        initPermission();\n    }, []);\n    // Define columns for the key contacts table\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.createColumns)([\n        {\n            accessorKey: \"name\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Key Contacts\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const keyContact = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    href: \"/key-contacts/edit?id=\".concat(keyContact.id),\n                    className: \"hover:underline font-medium\",\n                    children: [\n                        keyContact.firstName,\n                        \" \",\n                        keyContact.surname\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original1, _rowB_original, _rowB_original1;\n                const valueA = \"\".concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.firstName) || \"\", \" \").concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.surname) || \"\").trim();\n                const valueB = \"\".concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.firstName) || \"\", \" \").concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.surname) || \"\").trim();\n                return valueA.localeCompare(valueB);\n            },\n            filterFn: (row, _columnId, filterValue)=>{\n                const keyContact = row.original;\n                const fullName = \"\".concat(keyContact.firstName || \"\", \" \").concat(keyContact.surname || \"\").trim().toLowerCase();\n                return fullName.includes(filterValue.toLowerCase());\n            }\n        },\n        {\n            accessorKey: \"email\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Email\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const keyContact = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: keyContact.email || \"\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 24\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.email) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.email) || \"\";\n                return valueA.localeCompare(valueB);\n            },\n            filterFn: (row, _columnId, filterValue)=>{\n                const keyContact = row.original;\n                const email = (keyContact.email || \"\").toLowerCase();\n                return email.includes(filterValue.toLowerCase());\n            }\n        },\n        {\n            accessorKey: \"phone\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Phone\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const keyContact = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        keyContact.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: keyContact.phone\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 46\n                        }, this),\n                        keyContact.cellPhone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: keyContact.cellPhone\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 29\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original1, _rowB_original, _rowB_original1;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.phone) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.cellPhone) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.phone) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.cellPhone) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"address\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Address\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const keyContact = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: keyContact.address || \"\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 24\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.address) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.address) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"company\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Company\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                var _keyContact_company;\n                const keyContact = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: ((_keyContact_company = keyContact.company) === null || _keyContact_company === void 0 ? void 0 : _keyContact_company.title) || \"\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 24\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_company, _rowA_original, _rowB_original_company, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_company = _rowA_original.company) === null || _rowA_original_company === void 0 ? void 0 : _rowA_original_company.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_company = _rowB_original.company) === null || _rowB_original_company === void 0 ? void 0 : _rowB_original_company.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ListHeader, {\n                    title: \"Key Contacts\",\n                    actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: groupByCompany ? \"primary\" : \"secondary\",\n                                onClick: ()=>setGroupByCompany((prev)=>!prev),\n                                iconLeft: _barrel_optimize_names_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                children: \"Group by Company\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 29\n                            }, void 0),\n                            permission.EDIT_KEY_CONTACT && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/key-contacts/create\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    iconLeft: _barrel_optimize_names_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                    children: \"New Key Contact\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 37\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 33\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 25\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-32\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-primary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Loading key contacts...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 23\n                }, void 0),\n                title: \"Key Contacts\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_supplier_list_actions__WEBPACK_IMPORTED_MODULE_9__.SupplierListFilterActions, {\n                    permission: permission,\n                    groupByCompany: groupByCompany,\n                    setGroupByCompany: setGroupByCompany\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                lineNumber: 208,\n                columnNumber: 13\n            }, this),\n            keyContacts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center font-semibold w-full flex items-center justify-center h-32\",\n                children: \"No key contacts found\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                lineNumber: 220,\n                columnNumber: 17\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DataPresentation, {\n                data: keyContacts,\n                groupByCompany: groupByCompany,\n                columns: columns\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                lineNumber: 224,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(KeyContactList, \"eIofA4zwXe3KY+zQ+mmZ2Hn2Q0w=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_10__.useLazyQuery\n    ];\n});\n_c = KeyContactList;\nconst DataPresentation = (param)=>{\n    let { data, groupByCompany, columns } = param;\n    if (!groupByCompany) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.DataTable, {\n            columns: columns,\n            data: data,\n            showToolbar: true,\n            pageSize: 10,\n            showPageSizeSelector: true\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n            lineNumber: 245,\n            columnNumber: 13\n        }, undefined);\n    }\n    // Create columns for grouped view (without Company column)\n    const groupedColumns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.createColumns)([\n        {\n            accessorKey: \"name\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Key Contacts\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const keyContact = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    href: \"/key-contacts/edit?id=\".concat(keyContact.id),\n                    className: \"hover:underline font-medium\",\n                    children: [\n                        keyContact.firstName,\n                        \" \",\n                        keyContact.surname\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original1, _rowB_original, _rowB_original1;\n                const valueA = \"\".concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.firstName) || \"\", \" \").concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.surname) || \"\").trim();\n                const valueB = \"\".concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.firstName) || \"\", \" \").concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.surname) || \"\").trim();\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"email\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Email\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const keyContact = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: keyContact.email || \"\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 24\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.email) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.email) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"phone\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Phone\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const keyContact = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        keyContact.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: keyContact.phone\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 46\n                        }, undefined),\n                        keyContact.cellPhone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: keyContact.cellPhone\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original1, _rowB_original, _rowB_original1;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.phone) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.cellPhone) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.phone) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.cellPhone) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"address\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Address\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const keyContact = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: keyContact.address || \"\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 24\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.address) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.address) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n    const groupedData = data.reduce((prev, current)=>{\n        var _current_companyID;\n        const companyID = \"\".concat((_current_companyID = current.companyID) !== null && _current_companyID !== void 0 ? _current_companyID : 0);\n        if (Object.hasOwn(prev, companyID)) {\n            return prev;\n        }\n        const companyKeyContacts = data.filter((item)=>item.companyID === current.companyID);\n        return {\n            ...prev,\n            [companyID]: companyKeyContacts\n        };\n    }, {});\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: Object.entries(groupedData).map(function(grouped, index) {\n            var _keyContacts__company;\n            const [, keyContacts] = grouped;\n            const companyName = ((_keyContacts__company = keyContacts[0].company) === null || _keyContacts__company === void 0 ? void 0 : _keyContacts__company.title) || \"No Company\";\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" border font-bold border-gray-100 pl-5 max-w-sm rounded-lg mb-2.5 py-2 px-3\",\n                        children: companyName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.DataTable, {\n                        columns: groupedColumns,\n                        data: keyContacts,\n                        showToolbar: true,\n                        pageSize: 10,\n                        showPageSizeSelector: false\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, index, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\key-contacts\\\\key-contact-list.tsx\",\n                lineNumber: 365,\n                columnNumber: 21\n            }, this);\n        })\n    }, void 0, false);\n};\n_c1 = DataPresentation;\nvar _c, _c1;\n$RefreshReg$(_c, \"KeyContactList\");\n$RefreshReg$(_c1, \"DataPresentation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/key-contacts/key-contact-list.tsx\n"));

/***/ })

});