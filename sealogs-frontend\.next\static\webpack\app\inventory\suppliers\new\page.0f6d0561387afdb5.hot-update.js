"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/suppliers/new/page",{

/***/ "(app-pages-browser)/./src/app/ui/inventory/supplier-new.tsx":
/*!***********************************************!*\
  !*** ./src/app/ui/inventory/supplier-new.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewSupplier; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation_CREATE_SUPPLIER_CONTACT__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/lib/graphQL/mutation/CREATE_SUPPLIER_CONTACT */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/CREATE_SUPPLIER_CONTACT.ts\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _supplier_contacts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./supplier-contacts */ \"(app-pages-browser)/./src/app/ui/inventory/supplier-contacts.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NewSupplier(param) {\n    let { supplierId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [fileLinks, setFileLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [linkSelectedOption, setLinkSelectedOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [contactFields, setContactFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            name: \"\",\n            email: \"\",\n            phone: \"\"\n        }\n    ]);\n    const handleCreate = async ()=>{\n        var _response_data;\n        const name = document.getElementById(\"supplier-name\").value;\n        const website = document.getElementById(\"supplier-website\").value;\n        const phone = document.getElementById(\"supplier-phone\").value;\n        const email = document.getElementById(\"supplier-email\").value;\n        const address = document.getElementById(\"supplier-address\").value;\n        const notes = document.getElementById(\"supplier-notes\").value;\n        const variables = {\n            input: {\n                name,\n                address,\n                website,\n                email,\n                phone,\n                notes\n            }\n        };\n        if (name === \"\") {\n            return toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Please fill supplier's name!\"\n            });\n        }\n        // if (contactFields.length > 0) {\n        //     const anyContactFieldEmpty = contactFields.filter(\n        //         (contactField) =>\n        //             contactField.name == '' ||\n        //             (contactField.phone == '' && contactField.email == ''),\n        //     ).length\n        //     if (anyContactFieldEmpty > 0) {\n        //         return toast({\n        //             variant: 'destructive',\n        //             title: 'Error',\n        //             description: 'Please complete contact data!',\n        //         })\n        //     }\n        // }\n        const response = await mutationCreateSupplier({\n            variables\n        });\n        var _response_data_createSupplier_id;\n        const supplierID = (_response_data_createSupplier_id = (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.createSupplier.id) !== null && _response_data_createSupplier_id !== void 0 ? _response_data_createSupplier_id : 0;\n        if (supplierID == 0) {\n            return toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Error creating new supplier\"\n            });\n        }\n        contactFields.forEach(async (element)=>{\n            const variableContact = {\n                ...element,\n                supplierID\n            };\n            delete variableContact.id;\n            await mutationCreateSupplierContact({\n                variables: {\n                    input: variableContact\n                }\n            });\n        });\n        router.back();\n    };\n    const [mutationCreateSupplier, { loading: mutationcreateSupplierLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_SUPPLIER, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"mutationcreateSupplier error\", error);\n        }\n    });\n    const [mutationCreateSupplierContact, { loading: mutationcreateSupplierContactLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useMutation)(_app_lib_graphQL_mutation_CREATE_SUPPLIER_CONTACT__WEBPACK_IMPORTED_MODULE_10__.CREATE_SUPPLIER_CONTACT, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"mutationcreateSupplierContact error\", error);\n        }\n    });\n    const [createSeaLogsFileLinks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_SEALOGS_FILE_LINKS, {\n        onCompleted: (response)=>{\n            const data = response.createSeaLogsFileLinks;\n            if (data.id > 0) {\n                const newLinks = [\n                    ...fileLinks,\n                    data\n                ];\n                setFileLinks(newLinks);\n                linkSelectedOption ? setLinkSelectedOption([\n                    ...linkSelectedOption,\n                    {\n                        label: data.link,\n                        value: data.id\n                    }\n                ]) : setLinkSelectedOption([\n                    {\n                        label: data.link,\n                        value: data.id\n                    }\n                ]);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createSeaLogsFileLinksEntry error\", error);\n        }\n    });\n    const handleDeleteLink = (link)=>{\n        setLinkSelectedOption(linkSelectedOption.filter((l)=>l !== link));\n    };\n    const linkItem = (link)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-between align-middle mr-2 w-fit\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    href: link.label,\n                    target: \"_blank\",\n                    className: \"ml-2 \",\n                    children: link.label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-2 \",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        iconLeft: _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                        iconOnly: true,\n                        onClick: ()=>handleDeleteLink(link)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n            lineNumber: 172,\n            columnNumber: 13\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ListHeader, {\n                title: \"New Supplier\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                lineNumber: 191,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Card, {\n                className: \"mx-2.5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.H4, {\n                                children: \"Company Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.P, {\n                                children: \"Enter the basic information about the supplier company including contact details and address.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.CardContent, {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    label: \"Company Name\",\n                                    htmlFor: \"supplier-name\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        id: \"supplier-name\",\n                                        type: \"text\",\n                                        placeholder: \"Supplier name\",\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    label: \"Website\",\n                                    htmlFor: \"supplier-website\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        id: \"supplier-website\",\n                                        type: \"text\",\n                                        placeholder: \"Company website\",\n                                        className: \"w-full\",\n                                        onKeyDown: async (event)=>{\n                                            if (event.key === \"Enter\") {\n                                                const inputValue = event.target.value;\n                                                await createSeaLogsFileLinks({\n                                                    variables: {\n                                                        input: {\n                                                            link: inputValue\n                                                        }\n                                                    }\n                                                });\n                                                toast({\n                                                    title: \"Website added\",\n                                                    description: \"Added \".concat(inputValue, \" to supplier links\")\n                                                });\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 25\n                                }, this),\n                                (linkSelectedOption || fileLinks).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        label: \"Linked Websites\",\n                                        className: \"text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: linkSelectedOption ? linkSelectedOption.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-block\",\n                                                    children: linkItem(link)\n                                                }, link.value, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 55\n                                                }, this)) : fileLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-block\",\n                                                    children: linkItem(link)\n                                                }, link.value, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 55\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            label: \"Phone Number\",\n                                            htmlFor: \"supplier-phone\",\n                                            className: \"text-sm font-medium\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"supplier-phone\",\n                                                type: \"text\",\n                                                placeholder: \"Phone number\",\n                                                className: \"w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            label: \"Email Address\",\n                                            htmlFor: \"supplier-email\",\n                                            className: \"text-sm font-medium\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"supplier-email\",\n                                                type: \"email\",\n                                                placeholder: \"Email address\",\n                                                className: \"w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    label: \"Address\",\n                                    htmlFor: \"supplier-address\",\n                                    className: \"text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        id: \"supplier-address\",\n                                        rows: 3,\n                                        placeholder: \"Supplier address\",\n                                        className: \"w-full resize-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                lineNumber: 193,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Card, {\n                className: \"mx-2.5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.H4, {\n                                children: \"Contact Persons\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.P, {\n                                children: \"Enter the contact details (name, phone, and email) of the supplier's representative.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_supplier_contacts__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            data: contactFields,\n                            setData: setContactFields\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                lineNumber: 319,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Card, {\n                className: \"mx-2.5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.H4, {\n                                children: \"Additional Notes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.P, {\n                                children: \"Add any additional information about this supplier that might be useful.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            label: \"Notes\",\n                            htmlFor: \"supplier-notes\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                id: \"supplier-notes\",\n                                rows: 5,\n                                placeholder: \"Enter any additional notes about this supplier...\",\n                                className: \"w-full resize-none\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                lineNumber: 335,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_11__.FooterWrapper, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"back\",\n                        onClick: ()=>router.back(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: handleCreate,\n                        children: \"Create supplier\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                lineNumber: 355,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n        lineNumber: 190,\n        columnNumber: 9\n    }, this);\n}\n_s(NewSupplier, \"q/JrGpMPggXsrHp+ThyS7zkm6Zc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useMutation\n    ];\n});\n_c = NewSupplier;\nvar _c;\n$RefreshReg$(_c, \"NewSupplier\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvaW52ZW50b3J5L3N1cHBsaWVyLW5ldy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDdUM7QUFFSztBQUNoQjtBQUNpQjtBQUNNO0FBQ0o7QUFDRjtBQUVEO0FBRVo7QUFJRztBQUNRO0FBRWlEO0FBQ2pDO0FBUW5DO0FBQ2dEO0FBRXpELFNBQVNzQixZQUFZLEtBQXNDO1FBQXRDLEVBQUVDLFVBQVUsRUFBMEIsR0FBdEM7O0lBQ2hDLE1BQU1DLFNBQVNaLDBEQUFTQTtJQUN4QixNQUFNLEVBQUVhLEtBQUssRUFBRSxHQUFHakIsMERBQVFBO0lBQzFCLE1BQU0sQ0FBQ2tCLFdBQVdDLGFBQWEsR0FBRzFCLCtDQUFRQSxDQUFNLEVBQUU7SUFDbEQsTUFBTSxDQUFDMkIsb0JBQW9CQyxzQkFBc0IsR0FBRzVCLCtDQUFRQSxDQUFNLEVBQUU7SUFDcEUsTUFBTSxDQUFDNkIsZUFBZUMsaUJBQWlCLEdBQUc5QiwrQ0FBUUEsQ0FBcUI7UUFDbkU7WUFDSStCLE1BQU07WUFDTkMsT0FBTztZQUNQQyxPQUFPO1FBQ1g7S0FDSDtJQUVELE1BQU1DLGVBQWU7WUEyREVDO1FBMURuQixNQUFNSixPQUFPLFNBQ0FNLGNBQWMsQ0FBQyxpQkFDMUJDLEtBQUs7UUFDUCxNQUFNQyxVQUFVLFNBQ0hGLGNBQWMsQ0FBQyxvQkFDMUJDLEtBQUs7UUFDUCxNQUFNTCxRQUFRLFNBQ0RJLGNBQWMsQ0FBQyxrQkFDMUJDLEtBQUs7UUFDUCxNQUFNTixRQUFRLFNBQ0RLLGNBQWMsQ0FBQyxrQkFDMUJDLEtBQUs7UUFDUCxNQUFNRSxVQUFVLFNBQ0hILGNBQWMsQ0FBQyxvQkFDMUJDLEtBQUs7UUFDUCxNQUFNRyxRQUFRLFNBQ0RKLGNBQWMsQ0FBQyxrQkFDMUJDLEtBQUs7UUFFUCxNQUFNSSxZQUFZO1lBQ2RDLE9BQU87Z0JBQ0haO2dCQUNBUztnQkFDQUQ7Z0JBQ0FQO2dCQUNBQztnQkFDQVE7WUFDSjtRQUNKO1FBRUEsSUFBSVYsU0FBUyxJQUFJO1lBQ2IsT0FBT1AsTUFBTTtnQkFDVG9CLFNBQVM7Z0JBQ1RDLE9BQU87Z0JBQ1BDLGFBQWE7WUFDakI7UUFDSjtRQUVBLGtDQUFrQztRQUNsQyx5REFBeUQ7UUFDekQsNEJBQTRCO1FBQzVCLHlDQUF5QztRQUN6QyxzRUFBc0U7UUFDdEUsZUFBZTtRQUVmLHNDQUFzQztRQUN0Qyx5QkFBeUI7UUFDekIsc0NBQXNDO1FBQ3RDLDhCQUE4QjtRQUM5Qiw0REFBNEQ7UUFDNUQsYUFBYTtRQUNiLFFBQVE7UUFDUixJQUFJO1FBRUosTUFBTVgsV0FBVyxNQUFNWSx1QkFBdUI7WUFDMUNMO1FBQ0o7WUFFbUJQO1FBQW5CLE1BQU1hLGFBQWFiLENBQUFBLG9DQUFBQSxpQkFBQUEsU0FBU2MsSUFBSSxjQUFiZCxxQ0FBQUEsZUFBZWUsY0FBYyxDQUFDQyxFQUFFLGNBQWhDaEIsOENBQUFBLG1DQUFvQztRQUN2RCxJQUFJYSxjQUFjLEdBQUc7WUFDakIsT0FBT3hCLE1BQU07Z0JBQ1RvQixTQUFTO2dCQUNUQyxPQUFPO2dCQUNQQyxhQUFhO1lBQ2pCO1FBQ0o7UUFFQWpCLGNBQWN1QixPQUFPLENBQUMsT0FBT0M7WUFDekIsTUFBTUMsa0JBQWtCO2dCQUFFLEdBQUdELE9BQU87Z0JBQUVMO1lBQVc7WUFDakQsT0FBT00sZ0JBQWdCSCxFQUFFO1lBRXpCLE1BQU1JLDhCQUE4QjtnQkFDaENiLFdBQVc7b0JBQ1BDLE9BQU9XO2dCQUNYO1lBQ0o7UUFDSjtRQUVBL0IsT0FBT2lDLElBQUk7SUFDZjtJQUVBLE1BQU0sQ0FBQ1Qsd0JBQXdCLEVBQUVVLFNBQVNDLDZCQUE2QixFQUFFLENBQUMsR0FDdEV6RCw0REFBV0EsQ0FBQ1Esc0VBQWVBLEVBQUU7UUFDekJrRCxhQUFhLENBQUN4QixZQUFtQjtRQUNqQ3lCLFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDbEQ7SUFDSjtJQUVKLE1BQU0sQ0FDRk4sK0JBQ0EsRUFBRUUsU0FBU00sb0NBQW9DLEVBQUUsQ0FDcEQsR0FBRzlELDREQUFXQSxDQUFDVyx1R0FBdUJBLEVBQUU7UUFDckMrQyxhQUFhLENBQUN4QixZQUFtQjtRQUNqQ3lCLFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLHVDQUF1Q0E7UUFDekQ7SUFDSjtJQUVBLE1BQU0sQ0FBQ0csdUJBQXVCLEdBQUcvRCw0REFBV0EsQ0FBQ1MsZ0ZBQXlCQSxFQUFFO1FBQ3BFaUQsYUFBYSxDQUFDeEI7WUFDVixNQUFNYyxPQUFPZCxTQUFTNkIsc0JBQXNCO1lBQzVDLElBQUlmLEtBQUtFLEVBQUUsR0FBRyxHQUFHO2dCQUNiLE1BQU1jLFdBQVc7dUJBQUl4QztvQkFBV3dCO2lCQUFLO2dCQUNyQ3ZCLGFBQWF1QztnQkFDYnRDLHFCQUNNQyxzQkFBc0I7dUJBQ2ZEO29CQUNIO3dCQUFFdUMsT0FBT2pCLEtBQUtrQixJQUFJO3dCQUFFN0IsT0FBT1csS0FBS0UsRUFBRTtvQkFBQztpQkFDdEMsSUFDRHZCLHNCQUFzQjtvQkFDbEI7d0JBQUVzQyxPQUFPakIsS0FBS2tCLElBQUk7d0JBQUU3QixPQUFPVyxLQUFLRSxFQUFFO29CQUFDO2lCQUN0QztZQUNYO1FBQ0o7UUFDQVMsU0FBUyxDQUFDQztZQUNOQyxRQUFRRCxLQUFLLENBQUMscUNBQXFDQTtRQUN2RDtJQUNKO0lBRUEsTUFBTU8sbUJBQW1CLENBQUNEO1FBQ3RCdkMsc0JBQXNCRCxtQkFBbUIwQyxNQUFNLENBQUMsQ0FBQ0MsSUFBV0EsTUFBTUg7SUFDdEU7SUFFQSxNQUFNSSxXQUFXLENBQUNKO1FBQ2QscUJBQ0ksOERBQUNLO1lBQUlDLFdBQVU7OzhCQUNYLDhEQUFDdkUsaURBQUlBO29CQUFDd0UsTUFBTVAsS0FBS0QsS0FBSztvQkFBRVMsUUFBTztvQkFBU0YsV0FBVTs4QkFDN0NOLEtBQUtELEtBQUs7Ozs7Ozs4QkFFZiw4REFBQ007b0JBQUlDLFdBQVU7OEJBQ1gsNEVBQUNwRSx5REFBTUE7d0JBQ0h1QyxTQUFRO3dCQUNSZ0MsTUFBSzt3QkFDTEMsVUFBVXJFLDhFQUFDQTt3QkFDWHNFLFFBQVE7d0JBQ1JDLFNBQVMsSUFBTVgsaUJBQWlCRDs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFLcEQ7SUFFQSxxQkFDSSw4REFBQ0s7UUFBSUMsV0FBVTs7MEJBQ1gsOERBQUN0RCx1REFBVUE7Z0JBQUMwQixPQUFNOzs7Ozs7MEJBRWxCLDhEQUFDL0IsaURBQUlBO2dCQUFDMkQsV0FBVTs7a0NBQ1osOERBQUMxRCx1REFBVUE7OzBDQUNQLDhEQUFDRSwrQ0FBRUE7MENBQUM7Ozs7OzswQ0FDSiw4REFBQ0MsOENBQUNBOzBDQUFDOzs7Ozs7Ozs7Ozs7a0NBS1AsOERBQUNGLHdEQUFXQTt3QkFBQ3lELFdBQVU7a0NBQ25CLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ1gsOERBQUNuRSx1REFBS0E7b0NBQUM0RCxPQUFNO29DQUFlYyxTQUFROzhDQUNoQyw0RUFBQzdFLHVEQUFLQTt3Q0FDRmdELElBQUc7d0NBQ0g4QixNQUFLO3dDQUNMQyxhQUFZO3dDQUNaVCxXQUFVOzs7Ozs7Ozs7Ozs4Q0FJbEIsOERBQUNuRSx1REFBS0E7b0NBQUM0RCxPQUFNO29DQUFVYyxTQUFROzhDQUMzQiw0RUFBQzdFLHVEQUFLQTt3Q0FDRmdELElBQUc7d0NBQ0g4QixNQUFLO3dDQUNMQyxhQUFZO3dDQUNaVCxXQUFVO3dDQUNWVSxXQUFXLE9BQU9DOzRDQUNkLElBQUlBLE1BQU1DLEdBQUcsS0FBSyxTQUFTO2dEQUN2QixNQUFNQyxhQUFhLE1BQ1RYLE1BQU0sQ0FDZHJDLEtBQUs7Z0RBQ1AsTUFBTTBCLHVCQUF1QjtvREFDekJ0QixXQUFXO3dEQUNQQyxPQUFPOzREQUNId0IsTUFBTW1CO3dEQUNWO29EQUNKO2dEQUNKO2dEQUNBOUQsTUFBTTtvREFDRnFCLE9BQU87b0RBQ1BDLGFBQWEsU0FBb0IsT0FBWHdDLFlBQVc7Z0RBQ3JDOzRDQUNKO3dDQUNKOzs7Ozs7Ozs7OztnQ0FLTjNELENBQUFBLHNCQUFzQkYsU0FBUSxFQUFHOEQsTUFBTSxHQUFHLG1CQUN4Qyw4REFBQ2Y7b0NBQUlDLFdBQVU7OENBQ1gsNEVBQUNuRSx1REFBS0E7d0NBQ0Y0RCxPQUFNO3dDQUNOTyxXQUFVO2tEQUNWLDRFQUFDRDs0Q0FBSUMsV0FBVTtzREFDVjlDLHFCQUNLQSxtQkFBbUI2RCxHQUFHLENBQ2xCLENBQUNyQixxQkFJRyw4REFBQ0s7b0RBRUdDLFdBQVU7OERBQ1RGLFNBQVNKO21EQUZMQSxLQUFLN0IsS0FBSzs7Ozs0REFNM0JiLFVBQVUrRCxHQUFHLENBQ1QsQ0FBQ3JCLHFCQU1HLDhEQUFDSztvREFFR0MsV0FBVTs4REFDVEYsU0FBU0o7bURBRkxBLEtBQUs3QixLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FXakQsOERBQUNrQztvQ0FBSUMsV0FBVTs7c0RBQ1gsOERBQUNuRSx1REFBS0E7NENBQ0Y0RCxPQUFNOzRDQUNOYyxTQUFROzRDQUNSUCxXQUFVO3NEQUNWLDRFQUFDdEUsdURBQUtBO2dEQUNGZ0QsSUFBRztnREFDSDhCLE1BQUs7Z0RBQ0xDLGFBQVk7Z0RBQ1pULFdBQVU7Ozs7Ozs7Ozs7O3NEQUlsQiw4REFBQ25FLHVEQUFLQTs0Q0FDRjRELE9BQU07NENBQ05jLFNBQVE7NENBQ1JQLFdBQVU7c0RBQ1YsNEVBQUN0RSx1REFBS0E7Z0RBQ0ZnRCxJQUFHO2dEQUNIOEIsTUFBSztnREFDTEMsYUFBWTtnREFDWlQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBS3RCLDhEQUFDbkUsdURBQUtBO29DQUNGNEQsT0FBTTtvQ0FDTmMsU0FBUTtvQ0FDUlAsV0FBVTs4Q0FDViw0RUFBQ3JFLDZEQUFRQTt3Q0FDTCtDLElBQUc7d0NBQ0hzQyxNQUFNO3dDQUNOUCxhQUFZO3dDQUNaVCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU85Qiw4REFBQzNELGlEQUFJQTtnQkFBQzJELFdBQVU7O2tDQUNaLDhEQUFDMUQsdURBQVVBOzswQ0FDUCw4REFBQ0UsK0NBQUVBOzBDQUFDOzs7Ozs7MENBQ0osOERBQUNDLDhDQUFDQTswQ0FBQzs7Ozs7Ozs7Ozs7O2tDQUtQLDhEQUFDRix3REFBV0E7a0NBQ1IsNEVBQUNJLDJEQUFnQkE7NEJBQ2I2QixNQUFNcEI7NEJBQ042RCxTQUFTNUQ7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUtyQiw4REFBQ2hCLGlEQUFJQTtnQkFBQzJELFdBQVU7O2tDQUNaLDhEQUFDMUQsdURBQVVBOzswQ0FDUCw4REFBQ0UsK0NBQUVBOzBDQUFDOzs7Ozs7MENBQ0osOERBQUNDLDhDQUFDQTswQ0FBQzs7Ozs7Ozs7Ozs7O2tDQUtQLDhEQUFDRix3REFBV0E7a0NBQ1IsNEVBQUNWLHVEQUFLQTs0QkFBQzRELE9BQU07NEJBQVFjLFNBQVE7c0NBQ3pCLDRFQUFDNUUsNkRBQVFBO2dDQUNMK0MsSUFBRztnQ0FDSHNDLE1BQU07Z0NBQ05QLGFBQVk7Z0NBQ1pULFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTTFCLDhEQUFDNUQsc0VBQWFBOztrQ0FDViw4REFBQ1IseURBQU1BO3dCQUFDdUMsU0FBUTt3QkFBT21DLFNBQVMsSUFBTXhELE9BQU9pQyxJQUFJO2tDQUFJOzs7Ozs7a0NBR3JELDhEQUFDbkQseURBQU1BO3dCQUFDMEUsU0FBUzdDO2tDQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJL0M7R0EzVXdCYjs7UUFDTFYsc0RBQVNBO1FBQ05KLHNEQUFRQTtRQThGdEJOLHdEQUFXQTtRQVVYQSx3REFBV0E7UUFPa0JBLHdEQUFXQTs7O0tBakh4Qm9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvdWkvaW52ZW50b3J5L3N1cHBsaWVyLW5ldy50c3g/MDM0NyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXHJcblxyXG5pbXBvcnQgeyB1c2VNdXRhdGlvbiB9IGZyb20gJ0BhcG9sbG8vY2xpZW50J1xyXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXHJcbmltcG9ydCB7IElucHV0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2lucHV0J1xyXG5pbXBvcnQgeyBUZXh0YXJlYSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS90ZXh0YXJlYSdcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcclxuaW1wb3J0IHsgTGFiZWwgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvbGFiZWwnXHJcblxyXG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gJ0AvaG9va3MvdXNlLXRvYXN0J1xyXG5cclxuaW1wb3J0IHsgWCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcclxuaW1wb3J0IHtcclxuICAgIENSRUFURV9TVVBQTElFUixcclxuICAgIENSRUFURV9TRUFMT0dTX0ZJTEVfTElOS1MsXHJcbn0gZnJvbSAnQC9hcHAvbGliL2dyYXBoUUwvbXV0YXRpb24nXHJcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcclxuXHJcbmltcG9ydCB7IENSRUFURV9TVVBQTElFUl9DT05UQUNUIH0gZnJvbSAnQC9hcHAvbGliL2dyYXBoUUwvbXV0YXRpb24vQ1JFQVRFX1NVUFBMSUVSX0NPTlRBQ1QnXHJcbmltcG9ydCB7IEZvb3RlcldyYXBwZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvZm9vdGVyLXdyYXBwZXInXHJcbmltcG9ydCB7XHJcbiAgICBDYXJkLFxyXG4gICAgQ2FyZEhlYWRlcixcclxuICAgIENhcmRDb250ZW50LFxyXG4gICAgSDQsXHJcbiAgICBQLFxyXG4gICAgTGlzdEhlYWRlcixcclxufSBmcm9tICdAL2NvbXBvbmVudHMvdWknXHJcbmltcG9ydCBTdXBwbGllckNvbnRhY3RzLCB7IElTdXBwbGllckNvbnRhY3QgfSBmcm9tICcuL3N1cHBsaWVyLWNvbnRhY3RzJ1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTmV3U3VwcGxpZXIoeyBzdXBwbGllcklkIH06IHsgc3VwcGxpZXJJZDogbnVtYmVyIH0pIHtcclxuICAgIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXHJcbiAgICBjb25zdCB7IHRvYXN0IH0gPSB1c2VUb2FzdCgpXHJcbiAgICBjb25zdCBbZmlsZUxpbmtzLCBzZXRGaWxlTGlua3NdID0gdXNlU3RhdGU8YW55PihbXSlcclxuICAgIGNvbnN0IFtsaW5rU2VsZWN0ZWRPcHRpb24sIHNldExpbmtTZWxlY3RlZE9wdGlvbl0gPSB1c2VTdGF0ZTxhbnk+KFtdKVxyXG4gICAgY29uc3QgW2NvbnRhY3RGaWVsZHMsIHNldENvbnRhY3RGaWVsZHNdID0gdXNlU3RhdGU8SVN1cHBsaWVyQ29udGFjdFtdPihbXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBuYW1lOiAnJyxcclxuICAgICAgICAgICAgZW1haWw6ICcnLFxyXG4gICAgICAgICAgICBwaG9uZTogJycsXHJcbiAgICAgICAgfSxcclxuICAgIF0pXHJcblxyXG4gICAgY29uc3QgaGFuZGxlQ3JlYXRlID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgIGNvbnN0IG5hbWUgPSAoXHJcbiAgICAgICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdzdXBwbGllci1uYW1lJykgYXMgSFRNTElucHV0RWxlbWVudFxyXG4gICAgICAgICkudmFsdWVcclxuICAgICAgICBjb25zdCB3ZWJzaXRlID0gKFxyXG4gICAgICAgICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnc3VwcGxpZXItd2Vic2l0ZScpIGFzIEhUTUxJbnB1dEVsZW1lbnRcclxuICAgICAgICApLnZhbHVlXHJcbiAgICAgICAgY29uc3QgcGhvbmUgPSAoXHJcbiAgICAgICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdzdXBwbGllci1waG9uZScpIGFzIEhUTUxJbnB1dEVsZW1lbnRcclxuICAgICAgICApLnZhbHVlXHJcbiAgICAgICAgY29uc3QgZW1haWwgPSAoXHJcbiAgICAgICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdzdXBwbGllci1lbWFpbCcpIGFzIEhUTUxJbnB1dEVsZW1lbnRcclxuICAgICAgICApLnZhbHVlXHJcbiAgICAgICAgY29uc3QgYWRkcmVzcyA9IChcclxuICAgICAgICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3N1cHBsaWVyLWFkZHJlc3MnKSBhcyBIVE1MSW5wdXRFbGVtZW50XHJcbiAgICAgICAgKS52YWx1ZVxyXG4gICAgICAgIGNvbnN0IG5vdGVzID0gKFxyXG4gICAgICAgICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnc3VwcGxpZXItbm90ZXMnKSBhcyBIVE1MSW5wdXRFbGVtZW50XHJcbiAgICAgICAgKS52YWx1ZVxyXG5cclxuICAgICAgICBjb25zdCB2YXJpYWJsZXMgPSB7XHJcbiAgICAgICAgICAgIGlucHV0OiB7XHJcbiAgICAgICAgICAgICAgICBuYW1lLFxyXG4gICAgICAgICAgICAgICAgYWRkcmVzcyxcclxuICAgICAgICAgICAgICAgIHdlYnNpdGUsXHJcbiAgICAgICAgICAgICAgICBlbWFpbCxcclxuICAgICAgICAgICAgICAgIHBob25lLFxyXG4gICAgICAgICAgICAgICAgbm90ZXMsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBpZiAobmFtZSA9PT0gJycpIHtcclxuICAgICAgICAgICAgcmV0dXJuIHRvYXN0KHtcclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZScsXHJcbiAgICAgICAgICAgICAgICB0aXRsZTogJ0Vycm9yJyxcclxuICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBcIlBsZWFzZSBmaWxsIHN1cHBsaWVyJ3MgbmFtZSFcIixcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIGlmIChjb250YWN0RmllbGRzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAvLyAgICAgY29uc3QgYW55Q29udGFjdEZpZWxkRW1wdHkgPSBjb250YWN0RmllbGRzLmZpbHRlcihcclxuICAgICAgICAvLyAgICAgICAgIChjb250YWN0RmllbGQpID0+XHJcbiAgICAgICAgLy8gICAgICAgICAgICAgY29udGFjdEZpZWxkLm5hbWUgPT0gJycgfHxcclxuICAgICAgICAvLyAgICAgICAgICAgICAoY29udGFjdEZpZWxkLnBob25lID09ICcnICYmIGNvbnRhY3RGaWVsZC5lbWFpbCA9PSAnJyksXHJcbiAgICAgICAgLy8gICAgICkubGVuZ3RoXHJcblxyXG4gICAgICAgIC8vICAgICBpZiAoYW55Q29udGFjdEZpZWxkRW1wdHkgPiAwKSB7XHJcbiAgICAgICAgLy8gICAgICAgICByZXR1cm4gdG9hc3Qoe1xyXG4gICAgICAgIC8vICAgICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZScsXHJcbiAgICAgICAgLy8gICAgICAgICAgICAgdGl0bGU6ICdFcnJvcicsXHJcbiAgICAgICAgLy8gICAgICAgICAgICAgZGVzY3JpcHRpb246ICdQbGVhc2UgY29tcGxldGUgY29udGFjdCBkYXRhIScsXHJcbiAgICAgICAgLy8gICAgICAgICB9KVxyXG4gICAgICAgIC8vICAgICB9XHJcbiAgICAgICAgLy8gfVxyXG5cclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IG11dGF0aW9uQ3JlYXRlU3VwcGxpZXIoe1xyXG4gICAgICAgICAgICB2YXJpYWJsZXMsXHJcbiAgICAgICAgfSlcclxuXHJcbiAgICAgICAgY29uc3Qgc3VwcGxpZXJJRCA9IHJlc3BvbnNlLmRhdGE/LmNyZWF0ZVN1cHBsaWVyLmlkID8/IDBcclxuICAgICAgICBpZiAoc3VwcGxpZXJJRCA9PSAwKSB7XHJcbiAgICAgICAgICAgIHJldHVybiB0b2FzdCh7XHJcbiAgICAgICAgICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnLFxyXG4gICAgICAgICAgICAgICAgdGl0bGU6ICdFcnJvcicsXHJcbiAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogJ0Vycm9yIGNyZWF0aW5nIG5ldyBzdXBwbGllcicsXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb250YWN0RmllbGRzLmZvckVhY2goYXN5bmMgKGVsZW1lbnQpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgdmFyaWFibGVDb250YWN0ID0geyAuLi5lbGVtZW50LCBzdXBwbGllcklEIH1cclxuICAgICAgICAgICAgZGVsZXRlIHZhcmlhYmxlQ29udGFjdC5pZFxyXG5cclxuICAgICAgICAgICAgYXdhaXQgbXV0YXRpb25DcmVhdGVTdXBwbGllckNvbnRhY3Qoe1xyXG4gICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgaW5wdXQ6IHZhcmlhYmxlQ29udGFjdCxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgfSlcclxuXHJcbiAgICAgICAgcm91dGVyLmJhY2soKVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IFttdXRhdGlvbkNyZWF0ZVN1cHBsaWVyLCB7IGxvYWRpbmc6IG11dGF0aW9uY3JlYXRlU3VwcGxpZXJMb2FkaW5nIH1dID1cclxuICAgICAgICB1c2VNdXRhdGlvbihDUkVBVEVfU1VQUExJRVIsIHtcclxuICAgICAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZTogYW55KSA9PiB7fSxcclxuICAgICAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ211dGF0aW9uY3JlYXRlU3VwcGxpZXIgZXJyb3InLCBlcnJvcilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9KVxyXG5cclxuICAgIGNvbnN0IFtcclxuICAgICAgICBtdXRhdGlvbkNyZWF0ZVN1cHBsaWVyQ29udGFjdCxcclxuICAgICAgICB7IGxvYWRpbmc6IG11dGF0aW9uY3JlYXRlU3VwcGxpZXJDb250YWN0TG9hZGluZyB9LFxyXG4gICAgXSA9IHVzZU11dGF0aW9uKENSRUFURV9TVVBQTElFUl9DT05UQUNULCB7XHJcbiAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZTogYW55KSA9PiB7fSxcclxuICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdtdXRhdGlvbmNyZWF0ZVN1cHBsaWVyQ29udGFjdCBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgIH0sXHJcbiAgICB9KVxyXG5cclxuICAgIGNvbnN0IFtjcmVhdGVTZWFMb2dzRmlsZUxpbmtzXSA9IHVzZU11dGF0aW9uKENSRUFURV9TRUFMT0dTX0ZJTEVfTElOS1MsIHtcclxuICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLmNyZWF0ZVNlYUxvZ3NGaWxlTGlua3NcclxuICAgICAgICAgICAgaWYgKGRhdGEuaWQgPiAwKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBuZXdMaW5rcyA9IFsuLi5maWxlTGlua3MsIGRhdGFdXHJcbiAgICAgICAgICAgICAgICBzZXRGaWxlTGlua3MobmV3TGlua3MpXHJcbiAgICAgICAgICAgICAgICBsaW5rU2VsZWN0ZWRPcHRpb25cclxuICAgICAgICAgICAgICAgICAgICA/IHNldExpbmtTZWxlY3RlZE9wdGlvbihbXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ubGlua1NlbGVjdGVkT3B0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHsgbGFiZWw6IGRhdGEubGluaywgdmFsdWU6IGRhdGEuaWQgfSxcclxuICAgICAgICAgICAgICAgICAgICAgIF0pXHJcbiAgICAgICAgICAgICAgICAgICAgOiBzZXRMaW5rU2VsZWN0ZWRPcHRpb24oW1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHsgbGFiZWw6IGRhdGEubGluaywgdmFsdWU6IGRhdGEuaWQgfSxcclxuICAgICAgICAgICAgICAgICAgICAgIF0pXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ2NyZWF0ZVNlYUxvZ3NGaWxlTGlua3NFbnRyeSBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgIH0sXHJcbiAgICB9KVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZURlbGV0ZUxpbmsgPSAobGluazogYW55KSA9PiB7XHJcbiAgICAgICAgc2V0TGlua1NlbGVjdGVkT3B0aW9uKGxpbmtTZWxlY3RlZE9wdGlvbi5maWx0ZXIoKGw6IGFueSkgPT4gbCAhPT0gbGluaykpXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgbGlua0l0ZW0gPSAobGluazogYW55KSA9PiB7XHJcbiAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBhbGlnbi1taWRkbGUgbXItMiB3LWZpdFwiPlxyXG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj17bGluay5sYWJlbH0gdGFyZ2V0PVwiX2JsYW5rXCIgY2xhc3NOYW1lPVwibWwtMiBcIj5cclxuICAgICAgICAgICAgICAgICAgICB7bGluay5sYWJlbH1cclxuICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtMiBcIj5cclxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJpY29uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWNvbkxlZnQ9e1h9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGljb25Pbmx5XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZURlbGV0ZUxpbmsobGluayl9XHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApXHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxyXG4gICAgICAgICAgICA8TGlzdEhlYWRlciB0aXRsZT1cIk5ldyBTdXBwbGllclwiIC8+XHJcblxyXG4gICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJteC0yLjVcIj5cclxuICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgIDxIND5Db21wYW55IEluZm9ybWF0aW9uPC9IND5cclxuICAgICAgICAgICAgICAgICAgICA8UD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgRW50ZXIgdGhlIGJhc2ljIGluZm9ybWF0aW9uIGFib3V0IHRoZSBzdXBwbGllciBjb21wYW55XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGluY2x1ZGluZyBjb250YWN0IGRldGFpbHMgYW5kIGFkZHJlc3MuXHJcbiAgICAgICAgICAgICAgICAgICAgPC9QPlxyXG4gICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktOFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS01XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBsYWJlbD1cIkNvbXBhbnkgTmFtZVwiIGh0bWxGb3I9XCJzdXBwbGllci1uYW1lXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cInN1cHBsaWVyLW5hbWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlN1cHBsaWVyIG5hbWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGxhYmVsPVwiV2Vic2l0ZVwiIGh0bWxGb3I9XCJzdXBwbGllci13ZWJzaXRlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cInN1cHBsaWVyLXdlYnNpdGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkNvbXBhbnkgd2Vic2l0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbktleURvd249e2FzeW5jIChldmVudCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZXZlbnQua2V5ID09PSAnRW50ZXInKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBpbnB1dFZhbHVlID0gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV2ZW50LnRhcmdldCBhcyBIVE1MSW5wdXRFbGVtZW50XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLnZhbHVlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBjcmVhdGVTZWFMb2dzRmlsZUxpbmtzKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5wdXQ6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxpbms6IGlucHV0VmFsdWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0b2FzdCh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICdXZWJzaXRlIGFkZGVkJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogYEFkZGVkICR7aW5wdXRWYWx1ZX0gdG8gc3VwcGxpZXIgbGlua3NgLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIFdlYnNpdGUgTGlua3MgKi99XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHsobGlua1NlbGVjdGVkT3B0aW9uIHx8IGZpbGVMaW5rcykubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIkxpbmtlZCBXZWJzaXRlc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2xpbmtTZWxlY3RlZE9wdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gbGlua1NlbGVjdGVkT3B0aW9uLm1hcChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAobGluazoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogc3RyaW5nIHwgbnVtYmVyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiBzdHJpbmdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17bGluay52YWx1ZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1ibG9ja1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2xpbmtJdGVtKGxpbmspfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogZmlsZUxpbmtzLm1hcChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAobGluazoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHwgUmVhY3QuS2V5XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB8IG51bGxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHwgdW5kZWZpbmVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2xpbmsudmFsdWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtYmxvY2tcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtsaW5rSXRlbShsaW5rKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJQaG9uZSBOdW1iZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGh0bWxGb3I9XCJzdXBwbGllci1waG9uZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cInN1cHBsaWVyLXBob25lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlBob25lIG51bWJlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJFbWFpbCBBZGRyZXNzXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBodG1sRm9yPVwic3VwcGxpZXItZW1haWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJzdXBwbGllci1lbWFpbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW1haWwgYWRkcmVzc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIkFkZHJlc3NcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaHRtbEZvcj1cInN1cHBsaWVyLWFkZHJlc3NcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHRhcmVhXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJzdXBwbGllci1hZGRyZXNzXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3dzPXszfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU3VwcGxpZXIgYWRkcmVzc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHJlc2l6ZS1ub25lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxyXG4gICAgICAgICAgICA8L0NhcmQ+XHJcblxyXG4gICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJteC0yLjVcIj5cclxuICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgIDxIND5Db250YWN0IFBlcnNvbnM8L0g0PlxyXG4gICAgICAgICAgICAgICAgICAgIDxQPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBFbnRlciB0aGUgY29udGFjdCBkZXRhaWxzIChuYW1lLCBwaG9uZSwgYW5kIGVtYWlsKSBvZlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGUgc3VwcGxpZXIncyByZXByZXNlbnRhdGl2ZS5cclxuICAgICAgICAgICAgICAgICAgICA8L1A+XHJcbiAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPFN1cHBsaWVyQ29udGFjdHNcclxuICAgICAgICAgICAgICAgICAgICAgICAgZGF0YT17Y29udGFjdEZpZWxkc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0RGF0YT17c2V0Q29udGFjdEZpZWxkc31cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cclxuICAgICAgICAgICAgPC9DYXJkPlxyXG5cclxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwibXgtMi41XCI+XHJcbiAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgICA8SDQ+QWRkaXRpb25hbCBOb3RlczwvSDQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPFA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIEFkZCBhbnkgYWRkaXRpb25hbCBpbmZvcm1hdGlvbiBhYm91dCB0aGlzIHN1cHBsaWVyIHRoYXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWlnaHQgYmUgdXNlZnVsLlxyXG4gICAgICAgICAgICAgICAgICAgIDwvUD5cclxuICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudD5cclxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgbGFiZWw9XCJOb3Rlc1wiIGh0bWxGb3I9XCJzdXBwbGllci1ub3Rlc1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VGV4dGFyZWFcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwic3VwcGxpZXItbm90ZXNcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcm93cz17NX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgYW55IGFkZGl0aW9uYWwgbm90ZXMgYWJvdXQgdGhpcyBzdXBwbGllci4uLlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcmVzaXplLW5vbmVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxyXG4gICAgICAgICAgICA8L0NhcmQ+XHJcblxyXG4gICAgICAgICAgICA8Rm9vdGVyV3JhcHBlcj5cclxuICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImJhY2tcIiBvbkNsaWNrPXsoKSA9PiByb3V0ZXIuYmFjaygpfT5cclxuICAgICAgICAgICAgICAgICAgICBDYW5jZWxcclxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVDcmVhdGV9PkNyZWF0ZSBzdXBwbGllcjwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8L0Zvb3RlcldyYXBwZXI+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICApXHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VNdXRhdGlvbiIsIkxpbmsiLCJJbnB1dCIsIlRleHRhcmVhIiwiQnV0dG9uIiwiTGFiZWwiLCJ1c2VUb2FzdCIsIlgiLCJDUkVBVEVfU1VQUExJRVIiLCJDUkVBVEVfU0VBTE9HU19GSUxFX0xJTktTIiwidXNlUm91dGVyIiwiQ1JFQVRFX1NVUFBMSUVSX0NPTlRBQ1QiLCJGb290ZXJXcmFwcGVyIiwiQ2FyZCIsIkNhcmRIZWFkZXIiLCJDYXJkQ29udGVudCIsIkg0IiwiUCIsIkxpc3RIZWFkZXIiLCJTdXBwbGllckNvbnRhY3RzIiwiTmV3U3VwcGxpZXIiLCJzdXBwbGllcklkIiwicm91dGVyIiwidG9hc3QiLCJmaWxlTGlua3MiLCJzZXRGaWxlTGlua3MiLCJsaW5rU2VsZWN0ZWRPcHRpb24iLCJzZXRMaW5rU2VsZWN0ZWRPcHRpb24iLCJjb250YWN0RmllbGRzIiwic2V0Q29udGFjdEZpZWxkcyIsIm5hbWUiLCJlbWFpbCIsInBob25lIiwiaGFuZGxlQ3JlYXRlIiwicmVzcG9uc2UiLCJkb2N1bWVudCIsImdldEVsZW1lbnRCeUlkIiwidmFsdWUiLCJ3ZWJzaXRlIiwiYWRkcmVzcyIsIm5vdGVzIiwidmFyaWFibGVzIiwiaW5wdXQiLCJ2YXJpYW50IiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIm11dGF0aW9uQ3JlYXRlU3VwcGxpZXIiLCJzdXBwbGllcklEIiwiZGF0YSIsImNyZWF0ZVN1cHBsaWVyIiwiaWQiLCJmb3JFYWNoIiwiZWxlbWVudCIsInZhcmlhYmxlQ29udGFjdCIsIm11dGF0aW9uQ3JlYXRlU3VwcGxpZXJDb250YWN0IiwiYmFjayIsImxvYWRpbmciLCJtdXRhdGlvbmNyZWF0ZVN1cHBsaWVyTG9hZGluZyIsIm9uQ29tcGxldGVkIiwib25FcnJvciIsImVycm9yIiwiY29uc29sZSIsIm11dGF0aW9uY3JlYXRlU3VwcGxpZXJDb250YWN0TG9hZGluZyIsImNyZWF0ZVNlYUxvZ3NGaWxlTGlua3MiLCJuZXdMaW5rcyIsImxhYmVsIiwibGluayIsImhhbmRsZURlbGV0ZUxpbmsiLCJmaWx0ZXIiLCJsIiwibGlua0l0ZW0iLCJkaXYiLCJjbGFzc05hbWUiLCJocmVmIiwidGFyZ2V0Iiwic2l6ZSIsImljb25MZWZ0IiwiaWNvbk9ubHkiLCJvbkNsaWNrIiwiaHRtbEZvciIsInR5cGUiLCJwbGFjZWhvbGRlciIsIm9uS2V5RG93biIsImV2ZW50Iiwia2V5IiwiaW5wdXRWYWx1ZSIsImxlbmd0aCIsIm1hcCIsInJvd3MiLCJzZXREYXRhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/inventory/supplier-new.tsx\n"));

/***/ })

});