'use client'

import * as React from 'react'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useSidebar } from '@/components/ui/sidebar'
import Link from 'next/link'
import { SealogsCogIcon } from '@/app/lib/icons'

export const SupplierListFilterActions = ({
    permission,
    groupByCompany,
    setGroupByCompany,
}: {
    permission: any
    groupByCompany: boolean
    setGroupByCompany: (value: any) => void
}) => {
    const { isMobile } = useSidebar()

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <SealogsCogIcon size={36} />
            </DropdownMenuTrigger>
            <DropdownMenuContent
                side={isMobile ? 'bottom' : 'right'}
                align={isMobile ? 'end' : 'start'}>
                <DropdownMenuItem
                    onClick={() => setGroupByCompany((prev: any) => !prev)}>
                    Group by Company
                </DropdownMenuItem>
                <Link href={'/inventory/suppliers/new'}>
                    <DropdownMenuItem>New Supplier</DropdownMenuItem>
                </Link>
                {permission.EDIT_KEY_CONTACT && (
                    <Link href="/key-contacts/create">
                        <DropdownMenuItem>New Key Contact</DropdownMenuItem>
                    </Link>
                )}
            </DropdownMenuContent>
        </DropdownMenu>
    )
}
