"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/suppliers/new/page",{

/***/ "(app-pages-browser)/./src/app/ui/inventory/supplier-new.tsx":
/*!***********************************************!*\
  !*** ./src/app/ui/inventory/supplier-new.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewSupplier; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation_CREATE_SUPPLIER_CONTACT__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/lib/graphQL/mutation/CREATE_SUPPLIER_CONTACT */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/CREATE_SUPPLIER_CONTACT.ts\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _supplier_contacts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./supplier-contacts */ \"(app-pages-browser)/./src/app/ui/inventory/supplier-contacts.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NewSupplier(param) {\n    let { supplierId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [fileLinks, setFileLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [linkSelectedOption, setLinkSelectedOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [contactFields, setContactFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            name: \"\",\n            email: \"\",\n            phone: \"\"\n        }\n    ]);\n    const handleCreate = async ()=>{\n        var _response_data;\n        const name = document.getElementById(\"supplier-name\").value;\n        const website = document.getElementById(\"supplier-website\").value;\n        const phone = document.getElementById(\"supplier-phone\").value;\n        const email = document.getElementById(\"supplier-email\").value;\n        const address = document.getElementById(\"supplier-address\").value;\n        const notes = document.getElementById(\"supplier-notes\").value;\n        const variables = {\n            input: {\n                name,\n                address,\n                website,\n                email,\n                phone,\n                notes\n            }\n        };\n        if (name === \"\") {\n            return toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Please fill supplier's name!\"\n            });\n        }\n        // if (contactFields.length > 0) {\n        //     const anyContactFieldEmpty = contactFields.filter(\n        //         (contactField) =>\n        //             contactField.name == '' ||\n        //             (contactField.phone == '' && contactField.email == ''),\n        //     ).length\n        //     if (anyContactFieldEmpty > 0) {\n        //         return toast({\n        //             variant: 'destructive',\n        //             title: 'Error',\n        //             description: 'Please complete contact data!',\n        //         })\n        //     }\n        // }\n        const response = await mutationCreateSupplier({\n            variables\n        });\n        var _response_data_createSupplier_id;\n        const supplierID = (_response_data_createSupplier_id = (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.createSupplier.id) !== null && _response_data_createSupplier_id !== void 0 ? _response_data_createSupplier_id : 0;\n        if (supplierID == 0) {\n            return toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Error creating new supplier\"\n            });\n        }\n        contactFields.forEach(async (element)=>{\n            const variableContact = {\n                ...element,\n                supplierID\n            };\n            delete variableContact.id;\n            await mutationCreateSupplierContact({\n                variables: {\n                    input: variableContact\n                }\n            });\n        });\n        router.back();\n    };\n    const [mutationCreateSupplier, { loading: mutationcreateSupplierLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_SUPPLIER, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"mutationcreateSupplier error\", error);\n        }\n    });\n    const [mutationCreateSupplierContact, { loading: mutationcreateSupplierContactLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useMutation)(_app_lib_graphQL_mutation_CREATE_SUPPLIER_CONTACT__WEBPACK_IMPORTED_MODULE_10__.CREATE_SUPPLIER_CONTACT, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"mutationcreateSupplierContact error\", error);\n        }\n    });\n    const [createSeaLogsFileLinks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_SEALOGS_FILE_LINKS, {\n        onCompleted: (response)=>{\n            const data = response.createSeaLogsFileLinks;\n            if (data.id > 0) {\n                const newLinks = [\n                    ...fileLinks,\n                    data\n                ];\n                setFileLinks(newLinks);\n                linkSelectedOption ? setLinkSelectedOption([\n                    ...linkSelectedOption,\n                    {\n                        label: data.link,\n                        value: data.id\n                    }\n                ]) : setLinkSelectedOption([\n                    {\n                        label: data.link,\n                        value: data.id\n                    }\n                ]);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createSeaLogsFileLinksEntry error\", error);\n        }\n    });\n    const handleDeleteLink = (link)=>{\n        setLinkSelectedOption(linkSelectedOption.filter((l)=>l !== link));\n    };\n    const linkItem = (link)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-between align-middle mr-2 w-fit\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    href: link.label,\n                    target: \"_blank\",\n                    className: \"ml-2 \",\n                    children: link.label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-2 \",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        iconLeft: _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                        iconOnly: true,\n                        onClick: ()=>handleDeleteLink(link)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n            lineNumber: 172,\n            columnNumber: 13\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ListHeader, {\n                title: \"New Supplier\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                lineNumber: 191,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Card, {\n                className: \"mx-2.5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.H4, {\n                                children: \"Company Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.P, {\n                                children: \"Enter the basic information about the supplier company including contact details and address.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.CardContent, {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    label: \"Company Name\",\n                                    htmlFor: \"supplier-name\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        id: \"supplier-name\",\n                                        type: \"text\",\n                                        placeholder: \"Supplier name\",\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    label: \"Website\",\n                                    htmlFor: \"supplier-website\",\n                                    className: \"text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        id: \"supplier-website\",\n                                        type: \"text\",\n                                        placeholder: \"Company website\",\n                                        className: \"w-full\",\n                                        onKeyDown: async (event)=>{\n                                            if (event.key === \"Enter\") {\n                                                const inputValue = event.target.value;\n                                                await createSeaLogsFileLinks({\n                                                    variables: {\n                                                        input: {\n                                                            link: inputValue\n                                                        }\n                                                    }\n                                                });\n                                                toast({\n                                                    title: \"Website added\",\n                                                    description: \"Added \".concat(inputValue, \" to supplier links\")\n                                                });\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 25\n                                }, this),\n                                (linkSelectedOption || fileLinks).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        label: \"Linked Websites\",\n                                        className: \"text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: linkSelectedOption ? linkSelectedOption.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-block\",\n                                                    children: linkItem(link)\n                                                }, link.value, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 55\n                                                }, this)) : fileLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-block\",\n                                                    children: linkItem(link)\n                                                }, link.value, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 55\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            label: \"Phone Number\",\n                                            htmlFor: \"supplier-phone\",\n                                            className: \"text-sm font-medium\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"supplier-phone\",\n                                                type: \"text\",\n                                                placeholder: \"Phone number\",\n                                                className: \"w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            label: \"Email Address\",\n                                            htmlFor: \"supplier-email\",\n                                            className: \"text-sm font-medium\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"supplier-email\",\n                                                type: \"email\",\n                                                placeholder: \"Email address\",\n                                                className: \"w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    label: \"Address\",\n                                    htmlFor: \"supplier-address\",\n                                    className: \"text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        id: \"supplier-address\",\n                                        rows: 3,\n                                        placeholder: \"Supplier address\",\n                                        className: \"w-full resize-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                lineNumber: 193,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Card, {\n                className: \"mx-2.5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.H4, {\n                                children: \"Contact Persons\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.P, {\n                                children: \"Enter the contact details (name, phone, and email) of the supplier's representative.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_supplier_contacts__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            data: contactFields,\n                            setData: setContactFields\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                lineNumber: 322,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Card, {\n                className: \"mx-2.5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.H4, {\n                                children: \"Additional Notes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.P, {\n                                children: \"Add any additional information about this supplier that might be useful.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            label: \"Notes\",\n                            htmlFor: \"supplier-notes\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                id: \"supplier-notes\",\n                                rows: 5,\n                                placeholder: \"Enter any additional notes about this supplier...\",\n                                className: \"w-full resize-none\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                lineNumber: 338,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_11__.FooterWrapper, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"back\",\n                        onClick: ()=>router.back(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: handleCreate,\n                        children: \"Create supplier\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                lineNumber: 358,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n        lineNumber: 190,\n        columnNumber: 9\n    }, this);\n}\n_s(NewSupplier, \"q/JrGpMPggXsrHp+ThyS7zkm6Zc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useMutation\n    ];\n});\n_c = NewSupplier;\nvar _c;\n$RefreshReg$(_c, \"NewSupplier\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/inventory/supplier-new.tsx\n"));

/***/ })

});