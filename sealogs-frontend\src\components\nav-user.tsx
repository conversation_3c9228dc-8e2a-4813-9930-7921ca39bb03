'use client'
import { ChevronsUpDown, LogOut } from 'lucide-react'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuPortal,
    DropdownMenuSeparator,
    DropdownMenuSub,
    DropdownMenuSubContent,
    DropdownMenuSubTrigger,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    useSidebar,
} from '@/components/ui/sidebar'
import { useEffect, useState } from 'react'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { uniqueId } from 'lodash'
import { useUserback } from '@userback/react'
import SidebarTheme from './ThemeToggle'
export function NavUser({
    user,
}: {
    user: {
        name: string
        email: string
    }
}) {
    const [superAdmin, setSuperAdmin] = useState(false)
    const [admin, setAdmin] = useState(false)
    const [permissions, setPermissions] = useState<any>(false)
    const pathname = usePathname()
    const [settingsMenuItems, setSettingsMenuItems] = useState([] as any[])
    const { isMobile } = useSidebar()
    const { open, hide, show } = useUserback()

    let settingsMenuItemDefinitions = [
        {
            label: 'Crew Duties',
            url: '/settings/crew-duty/list',
            permission: 'EDIT_CREW_DUTY',
        },
        {
            label: 'Inventory Categories',
            url: '/settings/inventory/category',
            permission: 'EDIT_INVENTORY_CATEGORY',
        },
        {
            label: 'Maintenance Categories',
            url: '/settings/maintenance/category',
            permission: 'EDIT_TASK',
        },
        {
            label: 'User Roles',
            url: '/settings/user-role',
            permission: 'EDIT_GROUP',
        },
        {
            label: 'Departments',
            url: '/department',
            permission: 'EDIT_DEPARTMENT',
        },
        {
            label: 'Locations',
            url: '/location',
            permission: 'EDIT_LOCATION',
        },
    ]

    useEffect(() => {
        setPermissions(getPermissions)
    }, [])

    useEffect(() => {
        const settingItems: any[] = []

        const superAdmin = localStorage.getItem('superAdmin') === 'true'
        const admin = localStorage.getItem('admin') === 'true'
        setSuperAdmin(superAdmin)
        setAdmin(admin)

        settingsMenuItemDefinitions.forEach((item: any) => {
            if (
                (permissions && hasPermission(item.permission, permissions)) ||
                admin ||
                superAdmin
            ) {
                // This is temporary and not the correct way to check permissions and needs to be fixed.
                settingItems.push(
                    <li
                        key={`${item.label}-${uniqueId()}`}
                        className={`${pathname.includes(item.label) ? 'border-sllightblue-600' : 'border-transparent'}`}>
                        <Link href={item.url}>
                            <span>{item.label}</span>
                        </Link>
                    </li>,
                )
            }
        })
        setSettingsMenuItems(settingItems)
    }, [permissions])

    const openFeedbackModel = () => {
        open('general', 'form')
    }

    return (
        <SidebarMenu>
            <SidebarMenuItem>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <SidebarMenuButton
                            size="lg"
                            className="data-[state=open]:bg-outer-space-800 hover:bg-outer-space-800 bg-outer-space-600 pb-2.5 data-[state=open]:text-outer-space-50">
                            <div className="grid flex-1 text-left leading-tight text-outer-space-50 text-sm font-medium pb-2">
                                <span className="truncate ">{user.name}</span>
                                <span className="truncate text-xs font-normal text-outer-space-100">
                                    {user.email}
                                </span>
                            </div>
                            <ChevronsUpDown className="ml-auto size-6 text-outer-space-50" />
                        </SidebarMenuButton>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                        className="w-[--radix-dropdown-menu-trigger-width] bg-background rounded-lg"
                        side={isMobile ? 'bottom' : 'right'}
                        align="end"
                        sideOffset={4}>
                        <DropdownMenuLabel className="p-0 ">
                            <div className="flex items-center gap-2 px-1 py-1.5 text-left ">
                                <div className="grid flex-1 text-left leading-tight text-sm font-medium">
                                    <span className="truncate ">
                                        {user.name}
                                    </span>
                                    <span className="truncate text-xs font-normal text-muted-foreground">
                                        {user.email}
                                    </span>
                                </div>
                            </div>
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuGroup>
                            <SidebarTheme />
                            {superAdmin && (
                                <Link href={`/select-client`}>
                                    <DropdownMenuItem hoverEffect>
                                        Switch Client
                                    </DropdownMenuItem>
                                </Link>
                            )}
                            {(superAdmin || admin) && (
                                <Link href={`/company-details`}>
                                    <DropdownMenuItem hoverEffect>
                                        Company Details
                                    </DropdownMenuItem>
                                </Link>
                            )}
                            {(superAdmin || admin) && (
                                <Link href={`/select-department`}>
                                    <DropdownMenuItem hoverEffect>
                                        Select Department
                                    </DropdownMenuItem>
                                </Link>
                            )}
                            {/* <Collapsible
                className="group/collapsible"
              >
                <CollapsibleTrigger asChild>
                  <DropdownMenuItem>
                    Settings
                  </DropdownMenuItem>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <DropdownMenuItem>
                    Crew Duties
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    Inventory Categories
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    Maintenance Categories
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    User Roles
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    Departments
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    Locations
                  </DropdownMenuItem>
                </CollapsibleContent>
              </Collapsible> */}
                            <DropdownMenuSub>
                                <DropdownMenuSubTrigger>
                                    Settings
                                </DropdownMenuSubTrigger>
                                <DropdownMenuPortal>
                                    <DropdownMenuSubContent>
                                        <Link href={`/settings/crew-duty/list`}>
                                            <DropdownMenuItem>
                                                Crew Duties
                                            </DropdownMenuItem>
                                        </Link>
                                        <Link
                                            href={`/settings/inventory/category`}>
                                            <DropdownMenuItem>
                                                Inventory Categories
                                            </DropdownMenuItem>
                                        </Link>
                                        <Link
                                            href={`/settings/maintenance/category`}>
                                            <DropdownMenuItem>
                                                Maintenance Categories
                                            </DropdownMenuItem>
                                        </Link>
                                        <Link href={`/settings/user-role`}>
                                            <DropdownMenuItem>
                                                User Roles
                                            </DropdownMenuItem>
                                        </Link>
                                        <Link href={`/department`}>
                                            <DropdownMenuItem>
                                                Departments
                                            </DropdownMenuItem>
                                        </Link>
                                        <Link href={`/location`}>
                                            <DropdownMenuItem>
                                                Locations
                                            </DropdownMenuItem>
                                        </Link>
                                        <Link href={`/key-contacts`}>
                                            <DropdownMenuItem>
                                                Key Contacts
                                            </DropdownMenuItem>
                                        </Link>
                                        <Link href={`/trip-schedules`}>
                                            <DropdownMenuItem>
                                                Import Timetables
                                            </DropdownMenuItem>
                                        </Link>
                                        <Link href={`/trip-schedule-services`}>
                                            <DropdownMenuItem>
                                                Trip Schedule Services
                                            </DropdownMenuItem>
                                        </Link>
                                        <Link href={`/trip-report-schedules`}>
                                            <DropdownMenuItem>
                                                Trip Report Schedules
                                            </DropdownMenuItem>
                                        </Link>
                                    </DropdownMenuSubContent>
                                </DropdownMenuPortal>
                            </DropdownMenuSub>
                            <DropdownMenuSub>
                                <DropdownMenuSubTrigger>
                                    Help
                                </DropdownMenuSubTrigger>
                                <DropdownMenuPortal>
                                    <DropdownMenuSubContent>
                                        <Link
                                            href={`https://sealogsv2.tawk.help/`}
                                            target="_blank">
                                            <DropdownMenuItem>
                                                Help docs
                                            </DropdownMenuItem>
                                        </Link>

                                        <DropdownMenuItem
                                            onClick={() => openFeedbackModel()}>
                                            Feedback
                                        </DropdownMenuItem>
                                    </DropdownMenuSubContent>
                                </DropdownMenuPortal>
                            </DropdownMenuSub>
                        </DropdownMenuGroup>
                        <DropdownMenuSeparator />
                        <Link href="/logout">
                            <DropdownMenuItem>
                                <LogOut />
                                Log out
                            </DropdownMenuItem>
                        </Link>
                    </DropdownMenuContent>
                </DropdownMenu>
            </SidebarMenuItem>
        </SidebarMenu>
    )
}
