'use client'

import { useLazyQuery } from '@apollo/client'
import { useEffect, useState } from 'react'
import Link from 'next/link'
import { GET_KEY_CONTACTS } from '@/app/lib/graphQL/query'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { Button } from '@/components/ui/button'
import { Users, Plus } from 'lucide-react'
import { ListHeader } from '@/components/ui'
import { DataTable, createColumns } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { SupplierListFilterActions } from '@/components/filter/components/supplier-list-actions'

export interface IKeyContactPermission {
    EDIT_KEY_CONTACT?: boolean
    DELETE_KEY_CONTACT?: boolean
}

export default function KeyContactList() {
    const [keyContacts, setKeyContacts] = useState([] as any)
    const [groupByCompany, setGroupByCompany] = useState(false)
    const [permission, setPermission] = useState<IKeyContactPermission>({
        EDIT_KEY_CONTACT: undefined,
        DELETE_KEY_CONTACT: undefined,
    })

    const [queryGetKeyContacts, { loading }] = useLazyQuery(GET_KEY_CONTACTS, {
        fetchPolicy: 'cache-and-network',
        onError: (error: any) => {
            console.error('queryGetKeyContacts error', error)
        },
    })

    const loadData = async () => {
        const { data } = await queryGetKeyContacts()
        setKeyContacts(data?.readKeyContacts.nodes ?? [])
    }

    const initPermission = () => {
        const permissions = getPermissions('EDIT_KEY_CONTACT')

        Object.keys(permission).forEach((value) => {
            const hasThisPermission = hasPermission(value, permissions)

            setPermission((prev) => ({ ...prev, [value]: hasThisPermission }))
        })
    }

    useEffect(() => {
        loadData()
        initPermission()
    }, [])

    // Define columns for the key contacts table
    const columns = createColumns([
        {
            accessorKey: 'name',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Key Contacts" />
            ),
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const keyContact: any = row.original
                return (
                    <Link
                        href={`/key-contacts/edit?id=${keyContact.id}`}
                        className="hover:underline font-medium">
                        {keyContact.firstName} {keyContact.surname}
                    </Link>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA =
                    `${rowA?.original?.firstName || ''} ${rowA?.original?.surname || ''}`.trim()
                const valueB =
                    `${rowB?.original?.firstName || ''} ${rowB?.original?.surname || ''}`.trim()
                return valueA.localeCompare(valueB)
            },
            filterFn: (row: any, _columnId: string, filterValue: string) => {
                const keyContact = row.original
                const fullName =
                    `${keyContact.firstName || ''} ${keyContact.surname || ''}`
                        .trim()
                        .toLowerCase()
                return fullName.includes(filterValue.toLowerCase())
            },
        },
        {
            accessorKey: 'email',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Email" />
            ),
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const keyContact: any = row.original
                return <span>{keyContact.email || ''}</span>
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.email || ''
                const valueB = rowB?.original?.email || ''
                return valueA.localeCompare(valueB)
            },
            filterFn: (row: any, _columnId: string, filterValue: string) => {
                const keyContact = row.original
                const email = (keyContact.email || '').toLowerCase()
                return email.includes(filterValue.toLowerCase())
            },
        },
        {
            accessorKey: 'phone',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Phone" />
            ),
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const keyContact: any = row.original
                return (
                    <div>
                        {keyContact.phone && <div>{keyContact.phone}</div>}
                        {keyContact.cellPhone && (
                            <div>{keyContact.cellPhone}</div>
                        )}
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA =
                    rowA?.original?.phone || rowA?.original?.cellPhone || ''
                const valueB =
                    rowB?.original?.phone || rowB?.original?.cellPhone || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'address',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Address" />
            ),
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const keyContact: any = row.original
                return <span>{keyContact.address || ''}</span>
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.address || ''
                const valueB = rowB?.original?.address || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'company',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Company" />
            ),
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const keyContact: any = row.original
                return <span>{keyContact.company?.title || ''}</span>
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.company?.title || ''
                const valueB = rowB?.original?.company?.title || ''
                return valueA.localeCompare(valueB)
            },
        },
    ])

    if (loading) {
        return (
            <>
                <ListHeader
                    title="Key Contacts"
                    actions={
                        <div className="flex items-center gap-2">
                            <Button
                                variant={
                                    groupByCompany ? 'primary' : 'secondary'
                                }
                                onClick={() =>
                                    setGroupByCompany((prev) => !prev)
                                }
                                iconLeft={Users}>
                                Group by Company
                            </Button>
                            {permission.EDIT_KEY_CONTACT && (
                                <Link href="/key-contacts/create">
                                    <Button iconLeft={Plus}>
                                        New Key Contact
                                    </Button>
                                </Link>
                            )}
                        </div>
                    }
                />
                <div className="flex items-center justify-center h-32">
                    <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                        <span>Loading key contacts...</span>
                    </div>
                </div>
            </>
        )
    }

    return (
        <>
            <ListHeader
                icon={<Users className="h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]" />}
                title="Key Contacts"
                actions={
                    <SupplierListFilterActions
                        permission={permission}
                        groupByCompany={groupByCompany}
                        setGroupByCompany={setGroupByCompany}
                    />
                }
            />
            {keyContacts.length === 0 ? (
                <div className="text-center font-semibold w-full flex items-center justify-center h-32">
                    No key contacts found
                </div>
            ) : (
                <DataPresentation
                    data={keyContacts}
                    groupByCompany={groupByCompany}
                    columns={columns}
                />
            )}
        </>
    )
}

const DataPresentation = ({
    data,
    groupByCompany,
    columns,
}: {
    data: any
    groupByCompany: boolean
    columns: any
}) => {
    if (!groupByCompany) {
        return (
            <DataTable
                columns={columns}
                data={data}
                showToolbar={true}
                pageSize={10}
                showPageSizeSelector={true}
            />
        )
    }

    // Create columns for grouped view (without Company column)
    const groupedColumns = createColumns([
        {
            accessorKey: 'name',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Key Contacts" />
            ),
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const keyContact: any = row.original
                return (
                    <Link
                        href={`/key-contacts/edit?id=${keyContact.id}`}
                        className="hover:underline font-medium">
                        {keyContact.firstName} {keyContact.surname}
                    </Link>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA =
                    `${rowA?.original?.firstName || ''} ${rowA?.original?.surname || ''}`.trim()
                const valueB =
                    `${rowB?.original?.firstName || ''} ${rowB?.original?.surname || ''}`.trim()
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'email',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Email" />
            ),
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const keyContact: any = row.original
                return <span>{keyContact.email || ''}</span>
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.email || ''
                const valueB = rowB?.original?.email || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'phone',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Phone" />
            ),
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const keyContact: any = row.original
                return (
                    <div>
                        {keyContact.phone && <div>{keyContact.phone}</div>}
                        {keyContact.cellPhone && (
                            <div>{keyContact.cellPhone}</div>
                        )}
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA =
                    rowA?.original?.phone || rowA?.original?.cellPhone || ''
                const valueB =
                    rowB?.original?.phone || rowB?.original?.cellPhone || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'address',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Address" />
            ),
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const keyContact: any = row.original
                return <span>{keyContact.address || ''}</span>
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.address || ''
                const valueB = rowB?.original?.address || ''
                return valueA.localeCompare(valueB)
            },
        },
    ])

    const groupedData = data.reduce((prev: any, current: any) => {
        const companyID = `${current.companyID ?? 0}`

        if (Object.hasOwn(prev, companyID)) {
            return prev
        }

        const companyKeyContacts = data.filter(
            (item: any) => item.companyID === current.companyID,
        )

        return {
            ...prev,
            [companyID]: companyKeyContacts,
        }
    }, {})

    return (
        <>
            {Object.entries(groupedData).map(function (grouped, index) {
                const [, keyContacts] = grouped as any

                const companyName =
                    keyContacts[0].company?.title || 'No Company'
                return (
                    <div key={index}>
                        <div className="bg-accent border border-border text-accent-foreground font-bold border-gray-100 pl-5 max-w-sm rounded-lg mb-2.5 py-2 px-3">
                            {companyName}
                        </div>
                        <DataTable
                            columns={groupedColumns}
                            data={keyContacts}
                            showToolbar={true}
                            pageSize={10}
                            showPageSizeSelector={false}
                        />
                    </div>
                )
            })}
        </>
    )
}
